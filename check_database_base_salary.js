// 检查数据库中基本工资的实际保存情况
const mongoose = require('mongoose');
const Salary = require('./backend/models/Salary');
const Employee = require('./backend/models/Employee');
const { SALARY_CONFIG } = require('./backend/config/salaryConfig');

// 连接数据库
async function connectDB() {
    try {
        await mongoose.connect('mongodb://localhost:27017/mchrms');
        console.log('数据库连接成功');
    } catch (error) {
        console.error('数据库连接失败:', error);
        process.exit(1);
    }
}

// 检查数据库中的基本工资数据
async function checkDatabaseBaseSalary() {
    console.log('检查数据库中的基本工资数据...\n');

    try {
        // 获取所有薪资数据
        const salaries = await Salary.find({}).limit(10);
        console.log(`检查前 ${salaries.length} 条薪资记录:\n`);

        for (const salary of salaries) {
            const employeeId = salary.employeeId;
            const name = salary.name;
            const isProbation = salary.isProbation || salary.calculationResult?.isProbation || false;
            
            console.log(`员工: ${employeeId} - ${name}`);
            console.log(`试用期状态: ${isProbation}`);
            console.log(`保存的基本工资: ${salary.adjustedBaseSalary}`);
            console.log(`学历调整: ${salary.educationAdjustment || 0}`);
            console.log(`语言调整: ${salary.languageAdjustment || 0}`);
            console.log(`学历系数: ${salary.educationCoefficient || 1.0}`);
            console.log(`语言系数: ${salary.languageCoefficient || 1.0}`);
            
            // 计算期望的基本工资
            const baseSalary = SALARY_CONFIG.BASE_SALARY || 3500;
            const educationAdjustment = Number(salary.educationAdjustment) || 0;
            const languageAdjustment = Number(salary.languageAdjustment) || 0;
            
            let expectedBaseSalary = baseSalary + educationAdjustment + languageAdjustment;
            expectedBaseSalary = Math.round((expectedBaseSalary + Number.EPSILON) * 100) / 100;
            
            console.log(`期望的基本工资: ${expectedBaseSalary}`);
            
            const actualBaseSalary = Number(salary.adjustedBaseSalary);
            const difference = Math.abs(actualBaseSalary - expectedBaseSalary);
            
            if (difference > 0.01) {
                console.log(`❌ 基本工资不正确! 差异: ${difference}`);
                
                // 检查是否是80%的结果
                const possibleOriginal = Math.round((actualBaseSalary / 0.8 + Number.EPSILON) * 100) / 100;
                if (Math.abs(possibleOriginal - expectedBaseSalary) < 0.01) {
                    console.log(`🔍 确认: 基本工资被错误地应用了80%系数`);
                    console.log(`   实际保存: ${actualBaseSalary} (错误)`);
                    console.log(`   应该保存: ${expectedBaseSalary} (正确)`);
                }
            } else {
                console.log(`✅ 基本工资正确`);
            }
            
            console.log(`应发工资: ${salary.calculationResult?.totalMonthlySalary || '未计算'}`);
            console.log('-'.repeat(50));
        }

        // 检查员工基础数据
        console.log('\n检查员工基础数据:');
        const employees = await Employee.find({}).limit(5);
        
        for (const employee of employees) {
            console.log(`员工: ${employee.employeeId} - ${employee.name}`);
            console.log(`工作类型: ${employee.workType || '未设置'}`);
            console.log(`试用期结束日期: ${employee.probationEndDate || '未设置'}`);
            console.log(`学历: ${employee.finalEducation || employee.firstEducation || '未设置'}`);
            console.log(`语言水平: ${employee.languageLevel || '未设置'}`);
            console.log('-'.repeat(30));
        }

    } catch (error) {
        console.error('检查过程中出错:', error);
    }
}

// 主函数
async function main() {
    await connectDB();
    await checkDatabaseBaseSalary();
    await mongoose.connection.close();
    console.log('检查完成');
}

main().catch(error => {
    console.error('检查过程出错:', error);
    process.exit(1);
});
