// 修复所有试用期基本工资相关问题
const { calculateMonthlySalary } = require('./backend/utils/SalaryCalculator');

// 测试函数：验证后端计算是否正确
function testBackendCalculation() {
    console.log('=== 测试后端计算逻辑 ===\n');

    // 测试用例：试用期员工
    const probationEmployee = {
        employeeId: 'TEST001',
        name: '试用期员工',
        positionType: '技术',
        positionLevel: 'A5',
        education: '本科（普通院校）',
        languageLevel: '熟练',
        administrativeLevel: '无',
        performanceCoefficient: 1.0,
        actualAttendance: 22,
        specialAllowance: { amount: 0 },
        specialDeduction: { amount: 0 },
        isProbation: true,
        workType: '试用',
        probationEndDate: '2025-12-31'
    };

    // 测试用例：正式员工
    const regularEmployee = {
        ...probationEmployee,
        name: '正式员工',
        employeeId: 'TEST002',
        isProbation: false,
        workType: '全职',
        probationEndDate: ''
    };

    console.log('试用期员工计算结果:');
    const probationResult = calculateMonthlySalary(probationEmployee);
    console.log('- 基本工资:', probationResult.adjustedBaseSalary);
    console.log('- 岗位工资:', probationResult.positionSalary);
    console.log('- 应发工资:', probationResult.calculationResult.totalMonthlySalary);
    console.log('- 原始总薪资:', probationResult.calculationResult.originalTotalSalary);
    console.log('- 试用期状态:', probationResult.isProbation);

    console.log('\n正式员工计算结果:');
    const regularResult = calculateMonthlySalary(regularEmployee);
    console.log('- 基本工资:', regularResult.adjustedBaseSalary);
    console.log('- 岗位工资:', regularResult.positionSalary);
    console.log('- 应发工资:', regularResult.calculationResult.totalMonthlySalary);
    console.log('- 原始总薪资:', regularResult.calculationResult.originalTotalSalary || '无');
    console.log('- 试用期状态:', regularResult.isProbation);

    console.log('\n=== 验证结果 ===');
    
    // 验证基本工资是否相同
    const baseSalaryEqual = probationResult.adjustedBaseSalary === regularResult.adjustedBaseSalary;
    console.log('1. 基本工资是否相同:', baseSalaryEqual ? '✓ 正确' : '✗ 错误');
    console.log('   试用期:', probationResult.adjustedBaseSalary, '正式:', regularResult.adjustedBaseSalary);

    // 验证岗位工资是否相同
    const positionSalaryEqual = probationResult.positionSalary === regularResult.positionSalary;
    console.log('2. 岗位工资是否相同:', positionSalaryEqual ? '✓ 正确' : '✗ 错误');
    console.log('   试用期:', probationResult.positionSalary, '正式:', regularResult.positionSalary);

    // 验证试用期80%计算
    const expectedProbationSalary = Math.round(regularResult.calculationResult.totalMonthlySalary * 0.8);
    const probationSalaryCorrect = Math.abs(probationResult.calculationResult.totalMonthlySalary - expectedProbationSalary) < 1;
    console.log('3. 试用期80%计算是否正确:', probationSalaryCorrect ? '✓ 正确' : '✗ 错误');
    console.log('   期望:', expectedProbationSalary, '实际:', probationResult.calculationResult.totalMonthlySalary);

    // 验证原始总薪资字段
    const originalTotalSalaryCorrect = probationResult.calculationResult.originalTotalSalary === regularResult.calculationResult.totalMonthlySalary;
    console.log('4. 原始总薪资字段是否正确:', originalTotalSalaryCorrect ? '✓ 正确' : '✗ 错误');
    console.log('   试用期原始总薪资:', probationResult.calculationResult.originalTotalSalary);
    console.log('   正式员工应发工资:', regularResult.calculationResult.totalMonthlySalary);

    const allCorrect = baseSalaryEqual && positionSalaryEqual && probationSalaryCorrect && originalTotalSalaryCorrect;
    
    if (allCorrect) {
        console.log('\n✅ 后端计算逻辑完全正确！');
    } else {
        console.log('\n❌ 后端计算逻辑有问题！');
    }

    return allCorrect;
}

// 生成修复脚本
function generateFixScript() {
    console.log('\n=== 生成修复脚本 ===\n');

    const fixScript = `
// 修复所有试用期基本工资显示问题的完整脚本

## 问题总结
1. 数据库中可能保存了错误的基本工资数据（80%后的值）
2. 前端显示逻辑需要确保显示原始值
3. 前端保存逻辑需要确保保存正确的数据

## 修复步骤

### 1. 检查并修复数据库数据
如果数据库中有错误的基本工资数据，需要运行数据库修复脚本。

### 2. 确保前端显示正确
- SalaryList.js: 基本工资列不显示试用期标识 ✓ 已修复
- SalaryForm.js: 基本工资显示原始值 ✓ 需要验证
- SalaryDetail.js: 基本工资显示原始值 ✓ 需要验证

### 3. 确保前端保存正确
- SalaryForm.js: 保存时使用后端返回的正确值 ✓ 需要验证

### 4. 确保状态同步正确
- 试用期状态判断逻辑 ✓ 已修复
- 试用期说明显示逻辑 ✓ 已修复

## 关键修复点

### 前端保存逻辑修复
在 SalaryForm.js 中，确保保存的是后端返回的正确值：

\`\`\`javascript
// 保存时使用后端返回的原始值
adjustedBaseSalary: salaryResult.adjustedBaseSalary,  // 这应该是原始值
originalBaseSalary: salaryResult.originalBaseSalary || salaryResult.adjustedBaseSalary,
\`\`\`

### 前端显示逻辑确认
在所有显示基本工资的地方，确保显示的是原始值：

1. SalaryList.js: 基本工资列
2. SalaryForm.js: 计算结果显示
3. SalaryDetail.js: 薪资详情显示

### 数据库数据修复
如果数据库中有错误数据，需要：
1. 识别被错误应用80%的基本工资
2. 恢复为正确的原始值
3. 验证修复结果

## 验证方法
1. 运行后端计算测试，确保计算逻辑正确
2. 检查前端显示，确保基本工资显示原始值
3. 测试保存功能，确保保存正确的数据
4. 测试试用期说明显示功能
`;

    console.log(fixScript);
}

// 主函数
function main() {
    console.log('试用期基本工资问题完整修复脚本');
    console.log('=====================================\n');

    // 1. 测试后端计算逻辑
    const backendCorrect = testBackendCalculation();

    if (backendCorrect) {
        console.log('\n后端计算逻辑正确，问题主要在前端显示和数据保存。');
    } else {
        console.log('\n后端计算逻辑有问题，需要先修复后端。');
        return;
    }

    // 2. 生成修复指导
    generateFixScript();

    console.log('\n=== 立即需要执行的修复 ===');
    console.log('1. 检查数据库中是否有错误的基本工资数据');
    console.log('2. 确保前端显示逻辑正确');
    console.log('3. 确保前端保存逻辑正确');
    console.log('4. 测试完整的薪资计算流程');

    console.log('\n=== 关键原则 ===');
    console.log('❗ 试用期80%只应用于最终的应发工资');
    console.log('❗ 基本工资、岗位工资、津贴等必须显示原始值');
    console.log('❗ 数据库中保存的基本工资必须是原始值');
    console.log('❗ 前端显示的基本工资必须是原始值');
}

// 运行修复脚本
main();
