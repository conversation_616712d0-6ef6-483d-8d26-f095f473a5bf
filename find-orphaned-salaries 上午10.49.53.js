const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// 定义简化的模型结构
const EmployeeSchema = new Schema({
  employeeId: String,
  name: String
});

const SalarySchema = new Schema({
  employeeId: String,
  name: String
});

// 创建模型
const Employee = mongoose.model("Employee", EmployeeSchema, "employees");
const Salary = mongoose.model("Salary", SalarySchema, "salaries");

mongoose.connect("mongodb://localhost:27017/salary_management")
.then(async () => {
  console.log('MongoDB连接成功');
  
  // 查找所有薪资记录
  const allSalaries = await Salary.find({});
  console.log(`总共找到 ${allSalaries.length} 条薪资记录`);
  
  // 获取所有员工ID
  const employees = await Employee.find({});
  const employeeIds = employees.map(emp => emp.employeeId);
  console.log(`总共找到 ${employees.length} 名员工`);
  
  // 查找孤立的薪资记录
  const orphanedSalaries = allSalaries.filter(salary => 
    !employeeIds.includes(salary.employeeId)
  );
  
  console.log('孤立的薪资记录:');
  orphanedSalaries.forEach(salary => {
    console.log(`ID: ${salary._id}, 员工ID: ${salary.employeeId}, 姓名: ${salary.name || '未知'}`);
  });
  
  if (orphanedSalaries.length > 0) {
    // 删除孤立记录
    for (const salary of orphanedSalaries) {
      await Salary.deleteOne({ _id: salary._id });
      console.log(`已删除薪资记录: ${salary._id}, 员工ID: ${salary.employeeId}`);
    }
    console.log(`已删除 ${orphanedSalaries.length} 条孤立记录`);
  } else {
    console.log('没有找到孤立的薪资记录');
  }
  
  mongoose.disconnect();
  process.exit();
})
.catch(err => {
  console.error('MongoDB连接失败:', err);
  mongoose.disconnect();
  process.exit(1);
});
