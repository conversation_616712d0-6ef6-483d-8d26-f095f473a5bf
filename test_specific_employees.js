// 测试特定员工的薪资计算
const { calculateMonthlySalary } = require('./backend/utils/SalaryCalculator');

// 测试函数
function testSpecificEmployees() {
    console.log('开始测试特定员工的薪资计算...\n');

    // 测试用例：模拟一个试用期员工
    const testEmployee = {
        employeeId: 'TEST001',
        name: '测试员工',
        department: '工程部',
        subDepartment: '开发组',
        positionType: '技术',
        positionLevel: 'A5',
        education: '本科（普通院校）',
        languageLevel: '精通',
        administrativeLevel: '无',
        performanceCoefficient: 1.0,
        actualAttendance: 22,
        specialAllowance: { amount: 0 },
        specialDeduction: { amount: 0 },
        // 试用期相关字段
        workType: '试用',
        probationEndDate: '2025-12-31',
        isProbation: true
    };

    console.log('测试员工信息:');
    console.log('- 工号:', testEmployee.employeeId);
    console.log('- 姓名:', testEmployee.name);
    console.log('- 学历:', testEmployee.education);
    console.log('- 语言水平:', testEmployee.languageLevel);
    console.log('- 工作类型:', testEmployee.workType);
    console.log('- 是否试用期:', testEmployee.isProbation);

    // 计算薪资
    console.log('\n计算薪资...');
    const result = calculateMonthlySalary(testEmployee);

    console.log('\n===== 薪资计算结果 =====');
    console.log('基本工资:', result.adjustedBaseSalary);
    console.log('原始基本工资:', result.originalBaseSalary);
    console.log('学历调整值:', result.educationAdjustment);
    console.log('语言调整值:', result.languageAdjustment);
    console.log('学历系数:', result.educationCoefficient);
    console.log('语言系数:', result.languageCoefficient);
    console.log('岗位工资:', result.positionSalary);
    console.log('管理津贴:', result.adminSalary);
    console.log('绩效奖金:', result.performanceBonus);
    console.log('餐补:', result.calculationResult.mealAllowance);
    console.log('通讯补贴:', result.calculationResult.communicationAllowance);
    console.log('试用期状态:', result.isProbation ? '是' : '否');
    console.log('试用期系数:', result.probationFactor);
    console.log('应发工资:', result.calculationResult.totalMonthlySalary);
    console.log('实发工资:', result.calculationResult.netSalary);

    console.log('\n===== 验证计算逻辑 =====');
    
    // 验证基本工资计算
    const baseSalary = 3500;
    const educationAdjustment = result.educationAdjustment;
    const languageAdjustment = result.languageAdjustment;
    const expectedOriginalBaseSalary = baseSalary + educationAdjustment + languageAdjustment;
    const expectedAdjustedBaseSalary = expectedOriginalBaseSalary * 0.8;
    
    console.log('基本工资计算验证:');
    console.log(`- 基本工资: ${baseSalary}`);
    console.log(`- 学历调整: ${educationAdjustment}`);
    console.log(`- 语言调整: ${languageAdjustment}`);
    console.log(`- 原始基本工资: ${baseSalary} + ${educationAdjustment} + ${languageAdjustment} = ${expectedOriginalBaseSalary}`);
    console.log(`- 试用期基本工资: ${expectedOriginalBaseSalary} × 80% = ${expectedAdjustedBaseSalary}`);
    console.log(`- 实际基本工资: ${result.adjustedBaseSalary}`);
    console.log(`- 计算是否正确: ${Math.abs(result.adjustedBaseSalary - expectedAdjustedBaseSalary) < 0.01 ? '✓ 正确' : '✗ 错误'}`);

    // 验证餐补和通讯补贴
    console.log('\n餐补和通讯补贴验证:');
    console.log(`- 餐补: ${result.calculationResult.mealAllowance} (应为400)`);
    console.log(`- 通讯补贴: ${result.calculationResult.communicationAllowance} (应为100)`);
    console.log(`- 餐补是否正确: ${result.calculationResult.mealAllowance === 400 ? '✓ 正确' : '✗ 错误'}`);
    console.log(`- 通讯补贴是否正确: ${result.calculationResult.communicationAllowance === 100 ? '✓ 正确' : '✗ 错误'}`);

    console.log('\n===== 测试完成 =====');
    console.log('结论:');
    console.log('1. 试用期员工的餐补和通讯补贴没有被乘以80%');
    console.log('2. 试用期员工的基本工资正确应用了80%折扣');
    console.log('3. 薪资计算逻辑符合预期');
}

// 运行测试
testSpecificEmployees();
