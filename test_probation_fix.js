// 测试试用期员工薪资计算修复
const { calculateMonthlySalary } = require('./backend/utils/SalaryCalculator');

// 测试函数
function testProbationFix() {
    console.log('开始测试试用期员工薪资计算修复...\n');

    // 创建一个试用期员工
    const probationEmployee = {
        employeeId: 'M001',
        name: '张三',
        department: '工程部',
        subDepartment: '开发组',
        positionType: '技术',
        positionLevel: 'A5',
        education: '本科（普通院校）',
        languageLevel: '熟练',
        administrativeLevel: '无',
        performanceCoefficient: 1.0,
        actualAttendance: 22,
        specialAllowance: { amount: 0 },
        specialDeduction: { amount: 0 },
        // 试用期相关字段
        workType: '试用',
        probationEndDate: '2025-12-31', // 未来日期，表示在试用期内
        isProbation: true
    };

    // 创建一个相同条件的正式员工
    const regularEmployee = {
        ...probationEmployee,
        workType: '全职',
        probationEndDate: '',
        isProbation: false
    };

    // 计算薪资
    console.log('计算试用期员工薪资...');
    const probationResult = calculateMonthlySalary(probationEmployee);

    console.log('计算正式员工薪资...');
    const regularResult = calculateMonthlySalary(regularEmployee);

    // 打印结果
    console.log('\n===== 试用期员工薪资 =====');
    printSalaryResult(probationResult);

    console.log('\n===== 正式员工薪资 =====');
    printSalaryResult(regularResult);

    // 验证餐补和通讯补贴是否正确
    console.log('\n===== 验证餐补和通讯补贴 =====');
    console.log('试用期员工餐补:', probationResult.calculationResult.mealAllowance);
    console.log('正式员工餐补:', regularResult.calculationResult.mealAllowance);
    console.log('餐补是否相等:', probationResult.calculationResult.mealAllowance === regularResult.calculationResult.mealAllowance ? '✓ 正确' : '✗ 错误');

    console.log('试用期员工通讯补贴:', probationResult.calculationResult.communicationAllowance);
    console.log('正式员工通讯补贴:', regularResult.calculationResult.communicationAllowance);
    console.log('通讯补贴是否相等:', probationResult.calculationResult.communicationAllowance === regularResult.calculationResult.communicationAllowance ? '✓ 正确' : '✗ 错误');

    // 验证基本工资是否为80%
    console.log('\n===== 验证基本工资80%折扣 =====');
    const expectedBaseSalary = Math.round((regularResult.originalBaseSalary * 0.8 + Number.EPSILON) * 100) / 100;
    console.log('正式员工原始基本工资:', regularResult.originalBaseSalary);
    console.log('试用期员工基本工资:', probationResult.adjustedBaseSalary);
    console.log('期望的试用期基本工资 (80%):', expectedBaseSalary);
    console.log('基本工资80%折扣是否正确:', Math.abs(probationResult.adjustedBaseSalary - expectedBaseSalary) < 0.01 ? '✓ 正确' : '✗ 错误');

    // 验证总薪资是否为80%
    console.log('\n===== 验证总薪资80%折扣 =====');
    const probationTotalSalary = probationResult.calculationResult.totalMonthlySalary;
    const regularTotalSalary = regularResult.calculationResult.totalMonthlySalary;
    const expectedTotalSalary = Math.round((regularTotalSalary * 0.8 + Number.EPSILON) * 100) / 100;
    
    console.log('正式员工总薪资:', regularTotalSalary);
    console.log('试用期员工总薪资:', probationTotalSalary);
    console.log('期望的试用期总薪资 (80%):', expectedTotalSalary);
    console.log('总薪资80%折扣是否正确:', Math.abs(probationTotalSalary - expectedTotalSalary) < 0.01 ? '✓ 正确' : '✗ 错误');

    console.log('\n===== 测试完成 =====');
}

// 打印薪资结果
function printSalaryResult(result) {
    console.log(`基本工资: ${result.adjustedBaseSalary}`);
    console.log(`原始基本工资: ${result.originalBaseSalary || '未设置'}`);
    console.log(`岗位工资: ${result.positionSalary}`);
    console.log(`管理津贴: ${result.adminSalary}`);
    console.log(`绩效奖金: ${result.performanceBonus}`);
    console.log(`餐补: ${result.calculationResult.mealAllowance}`);
    console.log(`通讯补贴: ${result.calculationResult.communicationAllowance}`);
    console.log(`试用期状态: ${result.isProbation ? '是' : '否'}`);
    console.log(`试用期系数: ${result.probationFactor}`);
    console.log(`应发工资: ${result.calculationResult.totalMonthlySalary}`);
    console.log(`实发工资: ${result.calculationResult.netSalary}`);
}

// 运行测试
testProbationFix();
