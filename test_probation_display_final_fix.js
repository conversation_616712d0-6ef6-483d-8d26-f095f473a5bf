// 测试试用期应发工资显示最终修复
const { calculateMonthlySalary } = require('./backend/utils/SalaryCalculator');

// 导入前端工具函数
const { isProbationPeriod, processSalaryData } = require('./frontend/src/utils/salaryUtils');

// 模拟薪资配置
const mockConfig = {
    SALARY_CONFIG: {
        BASE_SALARY: 3500,
        MEAL_ALLOWANCE: 200,
        COMMUNICATION_ALLOWANCE: 100,
        INSURANCE_BASE: {
            type: 'fixed',
            fixedAmount: 5000
        },
        PERFORMANCE_LEVELS: {
            'A': 1.5,
            'B+': 1.3,
            'B': 1.1,
            'C': 0.8,
            'D': 0.5
        }
    }
};

// 模拟SalaryForm.js的初始化逻辑
function simulateSalaryFormInitialization(initialValues) {
    console.log('SalaryForm.js 初始化:');
    console.log('- 初始值isProbation:', initialValues.isProbation);
    console.log('- 初始值workType:', initialValues.workType);
    console.log('- 初始值probationEndDate:', initialValues.probationEndDate);
    
    // 修复后的逻辑：使用isProbationPeriod函数判断
    const probationStatus = isProbationPeriod(initialValues);
    console.log('- 修复后的试用期状态:', probationStatus);
    
    return probationStatus;
}

// 模拟SalaryDetail.js的数据处理逻辑
function simulateSalaryDetailProcessing(employee, config) {
    console.log('SalaryDetail.js 数据处理:');
    console.log('- 原始员工数据isProbation:', employee.isProbation);
    
    // 使用processSalaryData处理数据
    const processed = processSalaryData(employee, config);
    console.log('- 处理后的isProbation:', processed.isProbation);
    
    return processed;
}

// 模拟社保缴费基数显示逻辑
function simulateInsuranceBaseDisplay(processedEmployee, localConfig) {
    const resetSalary = Number(processedEmployee.adjustedBaseSalary) === 0;
    
    if (resetSalary) {
        return '¥0';
    }
    
    // 根据配置确定显示的社保基数
    if (localConfig?.SALARY_CONFIG?.INSURANCE_BASE?.type === 'fixed') {
        return `¥${localConfig.SALARY_CONFIG.INSURANCE_BASE.fixedAmount || 5000}`;
    } else {
        return `¥${processedEmployee.adjustedBaseSalary}`;
    }
}

// 测试函数
function testProbationDisplayFinalFix() {
    console.log('开始测试试用期应发工资显示最终修复...\n');

    // 测试用例：试用期员工
    const probationEmployee = {
        employeeId: 'TEST001',
        name: '试用期员工',
        department: '工程部',
        subDepartment: '开发组',
        positionType: '技术',
        positionLevel: 'A5',
        education: '本科（普通院校）',
        languageLevel: '熟练',
        administrativeLevel: '无',
        performanceCoefficient: 1.0,
        actualAttendance: 22,
        specialAllowance: { amount: 0 },
        specialDeduction: { amount: 0 },
        // 试用期相关字段
        workType: '试用',
        probationEndDate: '2025-12-31',
        isProbation: true,
        // 模拟保存后的薪资数据
        adjustedBaseSalary: 4200,
        positionSalary: 2000,
        adminSalary: 0,
        performanceBonus: 0,
        calculationResult: {
            totalMonthlySalary: 5360,
            netSalary: 4500,
            socialInsurance: 515.13,
            tax: 344.87,
            mealAllowance: 400,
            communicationAllowance: 100
        }
    };

    // 测试用例：正式员工
    const regularEmployee = {
        ...probationEmployee,
        name: '正式员工',
        employeeId: 'TEST002',
        workType: '全职',
        probationEndDate: '',
        isProbation: false,
        calculationResult: {
            ...probationEmployee.calculationResult,
            totalMonthlySalary: 6700,
            netSalary: 5840
        }
    };

    console.log('===== 问题1：保存后再打开不显示说明 =====');
    
    console.log('\n试用期员工测试:');
    const probationFormStatus = simulateSalaryFormInitialization(probationEmployee);
    const probationDetailProcessed = simulateSalaryDetailProcessing(probationEmployee, mockConfig);
    
    console.log('修复效果:');
    console.log('- SalaryForm初始化试用期状态:', probationFormStatus ? '✓ 正确' : '✗ 错误');
    console.log('- SalaryDetail处理后试用期状态:', probationDetailProcessed.isProbation ? '✓ 正确' : '✗ 错误');
    console.log('- 状态一致性:', probationFormStatus === probationDetailProcessed.isProbation ? '✓ 一致' : '✗ 不一致');
    
    console.log('\n正式员工测试:');
    const regularFormStatus = simulateSalaryFormInitialization(regularEmployee);
    const regularDetailProcessed = simulateSalaryDetailProcessing(regularEmployee, mockConfig);
    
    console.log('修复效果:');
    console.log('- SalaryForm初始化试用期状态:', regularFormStatus ? '✗ 错误' : '✓ 正确');
    console.log('- SalaryDetail处理后试用期状态:', regularDetailProcessed.isProbation ? '✗ 错误' : '✓ 正确');
    console.log('- 状态一致性:', regularFormStatus === regularDetailProcessed.isProbation ? '✓ 一致' : '✗ 不一致');

    console.log('\n===== 问题2：SalaryDetail中说明没有正常显示 =====');
    
    console.log('\n试用期员工SalaryDetail显示测试:');
    console.log('- processedEmployee.isProbation:', probationDetailProcessed.isProbation);
    if (probationDetailProcessed.isProbation) {
        console.log('✓ 应发工资旁边应该显示: "(试用期工资为正常工资的80%)"');
        console.log('✓ 试用期说明区域应该显示');
        console.log('✓ 薪资说明区域应该显示试用期说明');
    } else {
        console.log('✗ 试用期状态错误，不会显示说明');
    }
    
    console.log('\n正式员工SalaryDetail显示测试:');
    console.log('- processedEmployee.isProbation:', regularDetailProcessed.isProbation);
    if (!regularDetailProcessed.isProbation) {
        console.log('✓ 应发工资旁边不应该显示试用期说明');
        console.log('✓ 试用期说明区域不应该显示');
        console.log('✓ 薪资说明区域不应该显示试用期说明');
    } else {
        console.log('✗ 试用期状态错误，会错误显示说明');
    }

    console.log('\n===== 社保缴费基数显示修复验证 =====');
    
    console.log('\n试用期员工社保缴费基数:');
    const probationInsuranceBase = simulateInsuranceBaseDisplay(probationDetailProcessed, mockConfig);
    console.log('- 显示结果:', probationInsuranceBase);
    console.log('- 期望结果: ¥5000 (固定基数)');
    console.log('- 是否正确:', probationInsuranceBase === '¥5000' ? '✓ 正确' : '✗ 错误');
    
    console.log('\n正式员工社保缴费基数:');
    const regularInsuranceBase = simulateInsuranceBaseDisplay(regularDetailProcessed, mockConfig);
    console.log('- 显示结果:', regularInsuranceBase);
    console.log('- 期望结果: ¥5000 (固定基数)');
    console.log('- 是否正确:', regularInsuranceBase === '¥5000' ? '✓ 正确' : '✗ 错误');

    console.log('\n===== 后端计算验证 =====');
    
    console.log('\n后端计算试用期员工:');
    const backendProbationResult = calculateMonthlySalary(probationEmployee);
    console.log('- 后端isProbation:', backendProbationResult.isProbation);
    console.log('- 应发工资:', backendProbationResult.calculationResult.totalMonthlySalary);
    
    console.log('\n后端计算正式员工:');
    const backendRegularResult = calculateMonthlySalary(regularEmployee);
    console.log('- 后端isProbation:', backendRegularResult.isProbation);
    console.log('- 应发工资:', backendRegularResult.calculationResult.totalMonthlySalary);

    console.log('\n===== 修复总结 =====');
    console.log('修复内容:');
    console.log('1. ✓ 修复SalaryForm.js初始化时的试用期状态判断');
    console.log('2. ✓ 修复processSalaryData函数，确保正确设置试用期状态');
    console.log('3. ✓ 修复SalaryDetail.js中的社保缴费基数显示');
    console.log('4. ✓ 确保前后端试用期状态判断一致');
    
    console.log('\n修复后的行为:');
    console.log('- 保存后再打开：正确显示试用期说明');
    console.log('- SalaryDetail显示：正确显示试用期说明');
    console.log('- 社保缴费基数：根据配置显示固定基数');
    console.log('- 状态一致性：前端各组件状态一致');
    
    console.log('\n验证步骤:');
    console.log('1. 打开试用期员工薪资计算页面');
    console.log('2. 计算薪资并保存');
    console.log('3. 关闭后重新打开，检查是否显示试用期说明');
    console.log('4. 进入薪资详情页面，检查试用期说明显示');
    console.log('5. 检查社保缴费基数是否显示固定值5000');
}

// 运行测试
testProbationDisplayFinalFix();
