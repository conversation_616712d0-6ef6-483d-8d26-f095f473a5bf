// 测试试用期应发工资显示修复
const { calculateMonthlySalary } = require('./backend/utils/SalaryCalculator');

// 模拟前端试用期判断逻辑
function isProbationPeriod(employeeData) {
    if (!employeeData) return false;
    
    // 如果工作类型是试用，直接返回true
    if (employeeData.workType === '试用') return true;
    
    // 如果有isProbation字段且为true，返回true
    if (employeeData.isProbation === true) return true;
    
    // 如果有试用期结束日期，检查当前日期是否在试用期内
    if (employeeData.probationEndDate) {
        const today = new Date();
        const probationEndDate = new Date(employeeData.probationEndDate);
        return today <= probationEndDate;
    }
    
    return false;
}

// 模拟SalaryForm.js的显示逻辑
function simulateSalaryFormDisplay(salaryResult, isProbationState) {
    console.log('SalaryForm.js 应发工资显示:');
    console.log(`应发工资: ¥${salaryResult.calculationResult.totalMonthlySalary}`);
    
    if (isProbationState) {
        console.log('✓ 显示试用期说明: "(试用期工资为正常工资的80%)"');
        return true;
    } else {
        console.log('- 不显示试用期说明');
        return false;
    }
}

// 模拟SalaryDetail.js的显示逻辑
function simulateSalaryDetailDisplay(processedEmployee) {
    console.log('SalaryDetail.js 应发工资显示:');
    console.log(`应发工资: ¥${processedEmployee.calculationResult?.totalMonthlySalary}`);
    
    if (processedEmployee.isProbation) {
        console.log('✓ 显示试用期说明: "(试用期工资为正常工资的80%)"');
        return true;
    } else {
        console.log('- 不显示试用期说明');
        return false;
    }
}

// 测试函数
function testProbationDisplayFix() {
    console.log('开始测试试用期应发工资显示修复...\n');

    // 测试用例1：明确的试用期员工
    const probationEmployee = {
        employeeId: 'TEST001',
        name: '试用期员工',
        department: '工程部',
        subDepartment: '开发组',
        positionType: '技术',
        positionLevel: 'A5',
        education: '本科（普通院校）',
        languageLevel: '熟练',
        administrativeLevel: '无',
        performanceCoefficient: 1.0,
        actualAttendance: 22,
        specialAllowance: { amount: 0 },
        specialDeduction: { amount: 0 },
        // 试用期相关字段
        workType: '试用',
        probationEndDate: '2025-12-31',
        isProbation: true
    };

    // 测试用例2：正式员工
    const regularEmployee = {
        ...probationEmployee,
        name: '正式员工',
        employeeId: 'TEST002',
        workType: '全职',
        probationEndDate: '',
        isProbation: false
    };

    const testCases = [
        { name: '试用期员工', employee: probationEmployee },
        { name: '正式员工', employee: regularEmployee }
    ];

    testCases.forEach((testCase, index) => {
        console.log(`\n===== 测试用例${index + 1}: ${testCase.name} =====`);
        
        const employee = testCase.employee;
        console.log('员工信息:');
        console.log('- isProbation:', employee.isProbation);
        console.log('- workType:', employee.workType);
        console.log('- probationEndDate:', employee.probationEndDate);
        
        // 前端试用期判断
        const frontendIsProbation = isProbationPeriod(employee);
        console.log('前端试用期判断结果:', frontendIsProbation);
        
        // 后端计算
        console.log('\n后端计算:');
        const salaryResult = calculateMonthlySalary(employee);
        console.log('- 后端isProbation:', salaryResult.isProbation);
        console.log('- 应发工资:', salaryResult.calculationResult.totalMonthlySalary);
        console.log('- 原始总薪资:', salaryResult.calculationResult.originalTotalSalary || '无');
        
        // 模拟修复前的SalaryForm显示（使用初始的isProbation状态）
        console.log('\n修复前 - SalaryForm.js 显示:');
        const beforeFixFormDisplay = simulateSalaryFormDisplay(salaryResult, employee.isProbation);
        
        // 模拟修复后的SalaryForm显示（使用后端返回的isProbation状态）
        console.log('\n修复后 - SalaryForm.js 显示:');
        const afterFixFormDisplay = simulateSalaryFormDisplay(salaryResult, salaryResult.isProbation);
        
        // 模拟SalaryDetail显示
        console.log('\nSalaryDetail.js 显示:');
        const detailDisplay = simulateSalaryDetailDisplay(salaryResult);
        
        // 验证一致性
        console.log('\n一致性检查:');
        const isConsistentAfterFix = afterFixFormDisplay === detailDisplay;
        console.log('修复后 SalaryForm 和 SalaryDetail 显示是否一致:', isConsistentAfterFix ? '✓ 一致' : '✗ 不一致');
        
        if (employee.isProbation) {
            console.log('期望行为: 应该显示试用期说明');
            console.log('实际行为:', afterFixFormDisplay ? '✓ 显示了试用期说明' : '✗ 没有显示试用期说明');
        } else {
            console.log('期望行为: 不应该显示试用期说明');
            console.log('实际行为:', afterFixFormDisplay ? '✗ 错误显示了试用期说明' : '✓ 正确没有显示试用期说明');
        }
        
        console.log('-'.repeat(60));
    });

    console.log('\n===== 修复总结 =====');
    console.log('修复内容:');
    console.log('1. ✓ 在SalaryForm.js中添加了试用期应发工资说明');
    console.log('2. ✓ 在SalaryDetail.js中添加了试用期应发工资说明');
    console.log('3. ✓ 修复了SalaryForm.js中isProbation状态的更新逻辑');
    console.log('4. ✓ 确保前端显示与后端计算结果一致');
    
    console.log('\n修复后的行为:');
    console.log('- 试用期员工: 应发工资旁边显示"(试用期工资为正常工资的80%)"');
    console.log('- 正式员工: 应发工资旁边不显示任何说明');
    console.log('- SalaryForm.js 和 SalaryDetail.js 显示逻辑一致');
    console.log('- 前端状态与后端计算结果同步');
    
    console.log('\n验证方法:');
    console.log('1. 打开试用期员工的薪资计算页面');
    console.log('2. 点击"计算薪资"按钮');
    console.log('3. 检查应发工资旁边是否显示试用期说明');
    console.log('4. 进入薪资详情页面，检查应发工资旁边是否也显示试用期说明');
    console.log('5. 对比正式员工，确认不显示试用期说明');
}

// 运行测试
testProbationDisplayFix();
