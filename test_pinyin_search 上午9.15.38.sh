#!/bin/bash

# 拼音搜索功能测试脚本
# 用于在终端中测试拼音搜索功能
# 
# 使用方法：
# chmod +x test_pinyin_search.sh
# ./test_pinyin_search.sh

# 测试用例数组
declare -a TEST_CASES=(
  "li"         # 测试姓氏全拼音
  "ming"       # 测试名字第一个字的全拼音
  "liang"      # 测试名字第二个字的全拼音
  "liming"     # 测试姓+名第一个字的全拼音
  "mingliang"  # 测试名字两个字的全拼音
  "limingliang" # 测试全名全拼音
  "lml"        # 测试拼音首字母
  "liml"       # 测试姓全拼音+名首字母
  "lmingl"     # 测试姓首字母+名第一个字全拼音+名第二个字首字母
  "lin"        # 测试另一个姓氏全拼音
  "linml"      # 测试另一个姓全拼音+名首字母
)

# 测试名字数组
declare -a TEST_NAMES=(
  "李明亮"
  "林敏丽"
)

echo "开始测试拼音搜索功能..."
echo "===================="

# 使用 Node.js 运行测试
node -e "
const { checkNameMatch } = require('./src/utils/pinyinSearch');

// 测试用例
const testCases = [
  $(printf "'%s', " "${TEST_CASES[@]}")
];

// 测试名字
const testNames = [
  $(printf "'%s', " "${TEST_NAMES[@]}")
];

let passCount = 0;
let failCount = 0;
let totalTests = 0;

// 运行测试
testNames.forEach(name => {
  console.log(\`测试姓名: \${name}\`);
  console.log('------------------');
  
  testCases.forEach((searchText, index) => {
    totalTests++;
    console.log(\`测试 \${index + 1}: 搜索文本=\"\${searchText}\"\`);
    
    try {
      // 测试匹配
      const result = checkNameMatch(name, searchText);
      
      if (result) {
        console.log(\`  ✅ 匹配成功\`);
        passCount++;
      } else {
        console.log(\`  ❌ 匹配失败\`);
        failCount++;
      }
    } catch (error) {
      console.error(\`  ❌ 测试出错:\`, error);
      failCount++;
    }
    
    console.log('------------------');
  });
});

console.log('测试完成!');
console.log(\`通过: \${passCount}, 失败: \${failCount}, 总计: \${totalTests}\`);
console.log('====================');

// 如果有失败的测试，退出码为1
process.exit(failCount > 0 ? 1 : 0);
"

# 检查测试结果
if [ $? -eq 0 ]; then
  echo "✅ 所有测试通过!"
else
  echo "❌ 测试失败!"
fi
