// 测试 SalaryDetail 组件中的基本工资计算公式显示
const employee = {
    adjustedBaseSalary: 3150,
    educationAdjustment: -350,
    languageAdjustment: 0,
    educationCoefficient: 0.9,
    languageCoefficient: 1.0,
    isProbation: false
};

console.log('测试员工数据:', employee);

// 模拟基本工资计算公式显示
const formatCurrency = (value) => {
    if (!value && value !== 0) return '-';
    const roundedValue = Math.round((value + Number.EPSILON) * 100) / 100;
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(roundedValue).replace('¥', '');
};

const BASE_SALARY = 3500;

// 基本工资计算公式显示
const baseSalaryFormula = `基本工资计算: ${formatCurrency(BASE_SALARY)}`;
const educationAdjustmentFormula = Number(employee.educationAdjustment) >= 0
    ? ` + ${formatCurrency(Number(employee.educationAdjustment))}`
    : ` - ${formatCurrency(Math.abs(Number(employee.educationAdjustment)))}`;
const languageAdjustmentFormula = Number(employee.languageAdjustment) >= 0
    ? ` + ${formatCurrency(Number(employee.languageAdjustment))}`
    : ` - ${formatCurrency(Math.abs(Number(employee.languageAdjustment)))}`;
// 试用期员工的基本工资不应该显示"× 80%"，应该显示原始值
const resultFormula = ` = ${formatCurrency(Number(employee.adjustedBaseSalary))}`;

const formula = baseSalaryFormula + educationAdjustmentFormula + languageAdjustmentFormula + resultFormula;

console.log('基本工资计算公式显示:', formula);
