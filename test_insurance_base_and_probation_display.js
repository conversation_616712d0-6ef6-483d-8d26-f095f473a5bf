// 测试社保基数配置和试用期应发工资显示修复
const { calculateMonthlySalary } = require('./backend/utils/SalaryCalculator');

// 测试函数
function testInsuranceBaseAndProbationDisplay() {
    console.log('开始测试社保基数配置和试用期应发工资显示修复...\n');

    // 测试用例：试用期员工
    const probationEmployee = {
        employeeId: 'TEST001',
        name: '试用期员工',
        department: '工程部',
        subDepartment: '开发组',
        positionType: '技术',
        positionLevel: 'A5',
        education: '硕士（普通院校）', // 系数1.3
        languageLevel: '精通', // 系数1.3
        administrativeLevel: '无',
        performanceCoefficient: 1.0,
        actualAttendance: 22,
        specialAllowance: { amount: 0 },
        specialDeduction: { amount: 0 },
        // 试用期相关字段
        workType: '试用',
        probationEndDate: '2025-12-31',
        isProbation: true
    };

    // 相同条件的正式员工
    const regularEmployee = {
        ...probationEmployee,
        name: '正式员工',
        workType: '全职',
        probationEndDate: '',
        isProbation: false
    };

    console.log('测试员工信息:');
    console.log('- 学历:', probationEmployee.education, '(系数1.3)');
    console.log('- 语言水平:', probationEmployee.languageLevel, '(系数1.3)');
    console.log('- 岗位:', probationEmployee.positionType, probationEmployee.positionLevel);

    // 计算薪资
    console.log('\n===== 计算试用期员工薪资 =====');
    const probationResult = calculateMonthlySalary(probationEmployee);

    console.log('\n===== 计算正式员工薪资 =====');
    const regularResult = calculateMonthlySalary(regularEmployee);

    console.log('\n===== 社保基数配置验证 =====');
    
    // 检查配置
    const salaryConfig = require('./backend/config/salaryConfig');
    const { SALARY_CONFIG: CONFIG } = salaryConfig;
    
    console.log('社保基数配置:');
    console.log('- 类型:', CONFIG.INSURANCE_BASE?.type || '未配置');
    console.log('- 固定金额:', CONFIG.INSURANCE_BASE?.fixedAmount || '未配置');
    
    if (CONFIG.INSURANCE_BASE?.type === 'fixed') {
        console.log('✓ 配置正确：使用固定社保基数', CONFIG.INSURANCE_BASE.fixedAmount);
    } else {
        console.log('✗ 配置问题：应该使用固定社保基数，但当前配置为', CONFIG.INSURANCE_BASE?.type || '未配置');
    }

    console.log('\n===== 社保计算验证 =====');
    
    // 验证社保计算是否使用了正确的基数
    console.log('试用期员工社保计算:');
    console.log('- 基本工资:', probationResult.adjustedBaseSalary);
    console.log('- 社保总额:', probationResult.calculationResult.socialInsurance);
    
    console.log('\n正式员工社保计算:');
    console.log('- 基本工资:', regularResult.adjustedBaseSalary);
    console.log('- 社保总额:', regularResult.calculationResult.socialInsurance);
    
    // 如果使用固定基数，两个员工的社保应该相同
    if (CONFIG.INSURANCE_BASE?.type === 'fixed') {
        const socialInsuranceEqual = Math.abs(probationResult.calculationResult.socialInsurance - regularResult.calculationResult.socialInsurance) < 0.01;
        console.log('\n固定社保基数验证:');
        console.log('- 试用期员工社保:', probationResult.calculationResult.socialInsurance);
        console.log('- 正式员工社保:', regularResult.calculationResult.socialInsurance);
        console.log('- 社保金额是否相等:', socialInsuranceEqual ? '✓ 正确' : '✗ 错误');
        
        // 计算期望的社保金额（基于固定基数5000）
        const expectedPension = Math.min(Math.max(5000, 4638.88), 23194.6) * 0.08; // 养老保险
        const expectedMedical = Math.min(Math.max(5000, 4853), 24267) * 0.02; // 医疗保险
        const expectedUnemployment = Math.min(Math.max(5000, 4217), 21086) * 0.003; // 失业保险
        const expectedSupplementary = 8 * 0.016; // 大额医疗补充
        const expectedTotal = Math.round((expectedPension + expectedMedical + expectedUnemployment + expectedSupplementary + Number.EPSILON) * 100) / 100;
        
        console.log('\n期望的社保计算（基于固定基数5000）:');
        console.log('- 养老保险基数:', Math.min(Math.max(5000, 4638.88), 23194.6), '金额:', Math.round((expectedPension + Number.EPSILON) * 100) / 100);
        console.log('- 医疗保险基数:', Math.min(Math.max(5000, 4853), 24267), '金额:', Math.round((expectedMedical + Number.EPSILON) * 100) / 100);
        console.log('- 失业保险基数:', Math.min(Math.max(5000, 4217), 21086), '金额:', Math.round((expectedUnemployment + Number.EPSILON) * 100) / 100);
        console.log('- 大额医疗补充:', Math.round((expectedSupplementary + Number.EPSILON) * 100) / 100);
        console.log('- 期望总额:', expectedTotal);
        console.log('- 实际总额:', probationResult.calculationResult.socialInsurance);
        console.log('- 计算是否正确:', Math.abs(probationResult.calculationResult.socialInsurance - expectedTotal) < 0.01 ? '✓ 正确' : '✗ 错误');
    }

    console.log('\n===== 试用期应发工资显示验证 =====');
    
    console.log('试用期员工薪资信息:');
    console.log('- 基本工资:', probationResult.adjustedBaseSalary, '(应显示原始值)');
    console.log('- 岗位工资:', probationResult.positionSalary, '(应显示原始值)');
    console.log('- 餐补:', probationResult.calculationResult.mealAllowance, '(应显示原始值)');
    console.log('- 通讯补贴:', probationResult.calculationResult.communicationAllowance, '(应显示原始值)');
    console.log('- 应发工资:', probationResult.calculationResult.totalMonthlySalary, '(应显示80%后的值)');
    console.log('- 原始总薪资:', probationResult.calculationResult.originalTotalSalary, '(试用期员工应有此字段)');
    
    console.log('\n正式员工薪资信息:');
    console.log('- 基本工资:', regularResult.adjustedBaseSalary);
    console.log('- 岗位工资:', regularResult.positionSalary);
    console.log('- 餐补:', regularResult.calculationResult.mealAllowance);
    console.log('- 通讯补贴:', regularResult.calculationResult.communicationAllowance);
    console.log('- 应发工资:', regularResult.calculationResult.totalMonthlySalary);
    console.log('- 原始总薪资:', regularResult.calculationResult.originalTotalSalary || '无（正式员工不需要此字段）');

    // 验证试用期80%计算
    const expectedProbationSalary = Math.round(regularResult.calculationResult.totalMonthlySalary * 0.8);
    console.log('\n试用期80%计算验证:');
    console.log('- 正式员工应发工资:', regularResult.calculationResult.totalMonthlySalary);
    console.log('- 期望的试用期应发工资 (80%):', expectedProbationSalary);
    console.log('- 实际的试用期应发工资:', probationResult.calculationResult.totalMonthlySalary);
    console.log('- 计算是否正确:', Math.abs(probationResult.calculationResult.totalMonthlySalary - expectedProbationSalary) < 1 ? '✓ 正确' : '✗ 错误');

    console.log('\n===== 前端显示验证 =====');
    console.log('前端应该显示的内容:');
    console.log('1. SalaryForm.js 应发工资旁边应显示: "(试用期工资为正常工资的80%)"');
    console.log('2. SalaryDetail.js 应发工资旁边应显示: "(试用期工资为正常工资的80%)"');
    console.log('3. SalaryDetail.js 社保缴费基数应显示:', CONFIG.INSURANCE_BASE?.type === 'fixed' ? `固定值 ${CONFIG.INSURANCE_BASE.fixedAmount}` : `基本工资 ${probationResult.adjustedBaseSalary}`);

    console.log('\n===== 测试总结 =====');
    console.log('修复内容:');
    console.log('✓ 1. 试用期应发工资旁边添加了说明文字');
    console.log('✓ 2. 社保计算逻辑修复为使用配置的固定基数');
    console.log('✓ 3. 社保缴费基数显示修复为显示实际使用的基数');
    console.log('✓ 4. 保持了试用期员工各项薪资组成部分显示原始值的逻辑');
}

// 运行测试
testInsuranceBaseAndProbationDisplay();
