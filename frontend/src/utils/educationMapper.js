/**
 * 学历映射工具函数
 * 用于统一处理整个系统中的学历映射逻辑，避免不一致问题
 */

/**
 * 标准学历映射表
 * 将各种学历表述统一映射为系统标准格式
 */
const EDUCATION_MAPPING = {
    // 大专及以下类别
    '专科': '大专及以下',
    '大专': '大专及以下',
    '高职': '大专及以下',
    '职业技术学院': '大专及以下',
    '成人大专': '大专及以下',
    '网络大专': '大专及以下',
    '函授大专': '大专及以下',
    '自考大专': '大专及以下',
    '电大大专': '大专及以下',
    '大专及以下': '大专及以下', // 保持原值
    
    // 本科类别
    '本科': '本科（普通院校）',
    '学士': '本科（普通院校）',
    '成人本科': '大专及以下', // 非全日制本科按大专及以下处理
    '网络本科': '大专及以下', // 非全日制本科按大专及以下处理
    '函授本科': '大专及以下', // 非全日制本科按大专及以下处理
    '自考本科': '大专及以下', // 非全日制本科按大专及以下处理
    '电大本科': '大专及以下', // 非全日制本科按大专及以下处理
    '本科（普通院校）': '本科（普通院校）', // 保持原值
    '本科（985/211 院校）': '本科（985/211 院校）', // 保持原值
    
    // 硕士类别
    '硕士': '硕士（普通院校）',
    '研究生': '硕士（普通院校）',
    '硕士研究生': '硕士（普通院校）',
    '在职硕士': '硕士（普通院校）', // 硕士不区分全日制和非全日制
    '硕士（普通院校）': '硕士（普通院校）', // 保持原值
    '硕士（985/211 院校）': '硕士（985/211 院校）', // 保持原值
    
    // 博士类别
    '博士': '博士（普通）',
    '博士研究生': '博士（普通）',
    '在职博士': '博士（普通）', // 博士不区分全日制和非全日制
    '博士（普通）': '博士（普通）', // 保持原值
    '博士（985/211 院校）': '博士（985/211 院校）' // 保持原值
};

/**
 * 学历关键词匹配规则
 * 用于模糊匹配包含特定关键词的学历
 */
const EDUCATION_KEYWORDS = [
    {
        keywords: ['专科', '大专', '高职', '职业技术'],
        result: '大专及以下'
    },
    {
        keywords: ['本科', '学士'],
        checkSchoolType: true,
        normalResult: '本科（普通院校）',
        eliteResult: '本科（985/211 院校）'
    },
    {
        keywords: ['硕士', '研究生'],
        checkSchoolType: true,
        normalResult: '硕士（普通院校）',
        eliteResult: '硕士（985/211 院校）'
    },
    {
        keywords: ['博士'],
        checkSchoolType: true,
        normalResult: '博士（普通）',
        eliteResult: '博士（985/211 院校）'
    }
];

/**
 * 985/211院校关键词
 */
const ELITE_SCHOOL_KEYWORDS = ['985', '211', '重点', '一流', '双一流'];

/**
 * 非全日制教育类型关键词
 */
const NON_FULLTIME_KEYWORDS = ['成人', '网络', '函授', '自考', '电大', '远程', '业余', '夜大', '在职'];

/**
 * 统一学历映射函数
 * @param {string} education - 原始学历字符串
 * @param {string} schoolType - 学校类型（可选）
 * @param {string} educationType - 教育类型（全日制/非全日制，可选）
 * @returns {string} 映射后的标准学历格式
 */
export const mapEducation = (education, schoolType = '', educationType = '') => {
    // 输入验证
    if (!education || typeof education !== 'string') {
        console.warn('mapEducation: 无效的学历输入:', education);
        return '大专及以下'; // 默认返回最低学历
    }

    const edu = education.trim();
    
    // 1. 首先尝试精确匹配
    if (EDUCATION_MAPPING[edu]) {
        console.log('学历精确映射:', edu, '->', EDUCATION_MAPPING[edu]);
        return EDUCATION_MAPPING[edu];
    }

    // 2. 检查是否为非全日制本科，如果是则按大专及以下处理
    if (educationType && NON_FULLTIME_KEYWORDS.some(keyword => educationType.includes(keyword))) {
        if (edu.includes('本科') || edu.includes('学士')) {
            console.log('非全日制本科按大专及以下处理:', edu, educationType);
            return '大专及以下';
        }
    }

    // 3. 通过学历内容检查是否为非全日制本科
    if ((edu.includes('本科') || edu.includes('学士')) && 
        NON_FULLTIME_KEYWORDS.some(keyword => edu.includes(keyword))) {
        console.log('检测到非全日制本科，按大专及以下处理:', edu);
        return '大专及以下';
    }

    // 4. 关键词模糊匹配
    for (const rule of EDUCATION_KEYWORDS) {
        if (rule.keywords.some(keyword => edu.includes(keyword))) {
            if (rule.checkSchoolType) {
                // 需要检查学校类型
                const isEliteSchool = schoolType && ELITE_SCHOOL_KEYWORDS.some(keyword => 
                    schoolType.includes(keyword)
                ) || ELITE_SCHOOL_KEYWORDS.some(keyword => edu.includes(keyword));
                
                const result = isEliteSchool ? rule.eliteResult : rule.normalResult;
                console.log('学历关键词匹配:', edu, schoolType, '->', result);
                return result;
            } else {
                // 不需要检查学校类型
                console.log('学历关键词匹配:', edu, '->', rule.result);
                return rule.result;
            }
        }
    }

    // 5. 如果无法匹配，返回默认值并输出警告
    console.warn('学历映射失败，使用默认值:', edu, '-> 大专及以下');
    return '大专及以下';
};

/**
 * 获取学历系数
 * @param {string} education - 学历
 * @param {Object} config - 薪资配置对象
 * @returns {number} 学历系数
 */
export const getEducationCoefficient = (education, config) => {
    if (!education || !config?.SALARY_CONFIG?.EDUCATION_COEFFICIENT) {
        console.warn('getEducationCoefficient: 缺少必要参数');
        return 1.0; // 默认值
    }

    const mappedEducation = mapEducation(education);
    const coefficient = config.SALARY_CONFIG.EDUCATION_COEFFICIENT[mappedEducation];
    
    if (coefficient !== undefined) {
        console.log('获取学历系数:', education, '->', mappedEducation, '->', coefficient);
        return coefficient;
    }

    console.warn('未找到学历系数，使用默认值:', mappedEducation);
    return 1.0; // 默认值
};

/**
 * 计算学历调整值
 * @param {string} education - 学历
 * @param {Object} config - 薪资配置对象
 * @param {number} baseSalary - 基本工资（可选，从配置中获取）
 * @returns {number} 学历调整值
 */
export const calculateEducationAdjustment = (education, config, baseSalary = null) => {
    if (!education || !config?.SALARY_CONFIG) {
        console.warn('calculateEducationAdjustment: 缺少必要参数');
        return 0;
    }

    const coefficient = getEducationCoefficient(education, config);
    const salary = baseSalary || config.SALARY_CONFIG.BASE_SALARY || 3500;
    
    if (coefficient === 1.0) {
        return 0; // 系数为1时，调整值为0
    }

    const adjustment = salary * (coefficient - 1);
    console.log('计算学历调整值:', education, coefficient, salary, '->', adjustment);
    return Math.round(adjustment); // 四舍五入到整数
};

/**
 * 获取语言系数
 * @param {string} languageLevel - 语言等级
 * @param {Object} config - 薪资配置对象
 * @returns {number} 语言系数
 */
export const getLanguageCoefficient = (languageLevel, config) => {
    if (!languageLevel || languageLevel === '无' || !config?.SALARY_CONFIG?.LANGUAGE_COEFFICIENT) {
        console.log('getLanguageCoefficient: 语言等级为无或缺少配置，返回默认系数 1.0');
        return 1.0; // 默认值
    }

    const coefficient = config.SALARY_CONFIG.LANGUAGE_COEFFICIENT[languageLevel];
    
    if (coefficient !== undefined) {
        console.log('获取语言系数:', languageLevel, '->', coefficient);
        return coefficient;
    }

    console.warn('未找到语言系数，使用默认值:', languageLevel);
    return 1.0; // 默认值
};

/**
 * 计算语言调整值
 * @param {string} languageLevel - 语言等级
 * @param {Object} config - 薪资配置对象
 * @param {number} baseSalary - 基本工资（可选，从配置中获取）
 * @returns {number} 语言调整值
 */
export const calculateLanguageAdjustment = (languageLevel, config, baseSalary = null) => {
    if (!languageLevel || languageLevel === '无' || !config?.SALARY_CONFIG) {
        console.log('calculateLanguageAdjustment: 语言等级为无或缺少配置，返回调整值 0');
        return 0;
    }

    const coefficient = getLanguageCoefficient(languageLevel, config);
    const salary = baseSalary || config.SALARY_CONFIG.BASE_SALARY || 3500;
    
    if (coefficient === 1.0) {
        return 0; // 系数为1时，调整值为0
    }

    const adjustment = salary * (coefficient - 1);
    console.log('计算语言调整值:', languageLevel, coefficient, salary, '->', adjustment);
    return Math.round(adjustment); // 四舍五入到整数
};

/**
 * 批量映射学历（用于数据处理）
 * @param {Array} educationList - 学历数组
 * @returns {Array} 映射后的学历数组
 */
export const batchMapEducation = (educationList) => {
    if (!Array.isArray(educationList)) {
        console.warn('batchMapEducation: 输入不是数组');
        return [];
    }

    return educationList.map(education => mapEducation(education));
};

/**
 * 验证学历映射结果
 * @param {string} originalEducation - 原始学历
 * @param {string} mappedEducation - 映射后学历
 * @returns {boolean} 是否为有效的映射结果
 */
export const validateEducationMapping = (originalEducation, mappedEducation) => {
    const validResults = [
        '大专及以下',
        '本科（普通院校）',
        '本科（985/211 院校）',
        '硕士（普通院校）',
        '硕士（985/211 院校）',
        '博士（普通）',
        '博士（985/211 院校）'
    ];

    const isValid = validResults.includes(mappedEducation);
    if (!isValid) {
        console.error('无效的学历映射结果:', originalEducation, '->', mappedEducation);
    }

    return isValid;
};

/**
 * 获取所有支持的学历类型
 * @returns {Array} 标准学历类型数组
 */
export const getSupportedEducationTypes = () => {
    return [
        '大专及以下',
        '本科（普通院校）',
        '本科（985/211 院校）',
        '硕士（普通院校）',
        '硕士（985/211 院校）',
        '博士（普通）',
        '博士（985/211 院校）'
    ];
};

// 默认导出
export default {
    mapEducation,
    getEducationCoefficient,
    calculateEducationAdjustment,
    getLanguageCoefficient,
    calculateLanguageAdjustment,
    batchMapEducation,
    validateEducationMapping,
    getSupportedEducationTypes
}; 