# 试用期基本工资问题最终修复方案

## 问题确认

通过测试确认：
- ✅ **后端计算逻辑完全正确**：试用期80%只应用于最终应发工资，基本工资保存原始值
- ❌ **数据库中存在错误数据**：基本工资被错误地保存为80%后的值
- ❌ **前端显示错误数据**：显示的是数据库中错误保存的80%值

## 根本原因

数据库中的基本工资数据被错误地应用了试用期80%系数，导致：
- 试用期员工基本工资显示为80%后的值（如3360而不是4200）
- 前端显示的是数据库中错误的数据
- 用户看到的所有基本工资都是错误的

## 立即修复步骤

### 1. 数据库修复（最重要）

**方法1：使用MongoDB命令修复**
```javascript
// 连接到数据库
use mchrms

// 查找所有可能有问题的记录
db.salaries.find({
    $or: [
        { isProbation: true },
        { workType: "试用" },
        { "calculationResult.isProbation": true }
    ]
}).forEach(function(salary) {
    // 计算正确的基本工资
    var baseSalary = 3500;
    var educationAdjustment = salary.educationAdjustment || 0;
    var languageAdjustment = salary.languageAdjustment || 0;
    var correctBaseSalary = baseSalary + educationAdjustment + languageAdjustment;
    
    // 检查是否需要修复（允许1分钱的误差）
    if (Math.abs(salary.adjustedBaseSalary - correctBaseSalary) > 0.01) {
        print("修复员工 " + salary.employeeId + " - " + salary.name);
        print("  错误基本工资: " + salary.adjustedBaseSalary);
        print("  正确基本工资: " + correctBaseSalary);
        
        // 更新数据
        db.salaries.updateOne(
            { _id: salary._id },
            {
                $set: {
                    adjustedBaseSalary: correctBaseSalary,
                    originalBaseSalary: correctBaseSalary
                }
            }
        );
        print("  ✅ 已修复");
    } else {
        print("员工 " + salary.employeeId + " 基本工资正确，无需修复");
    }
});
```

**方法2：重新计算所有员工薪资**
```bash
# 在项目根目录运行
cd /Users/<USER>/mchrms

# 如果有重新计算薪资的脚本，运行它
# 这会强制重新计算所有员工的薪资，确保数据正确
```

### 2. 验证修复结果

**检查修复是否成功**：
```javascript
// 验证所有员工的基本工资是否正确
db.salaries.aggregate([
    {
        $project: {
            employeeId: 1,
            name: 1,
            isProbation: 1,
            adjustedBaseSalary: 1,
            educationAdjustment: { $ifNull: ["$educationAdjustment", 0] },
            languageAdjustment: { $ifNull: ["$languageAdjustment", 0] },
            expectedBaseSalary: {
                $add: [
                    3500,
                    { $ifNull: ["$educationAdjustment", 0] },
                    { $ifNull: ["$languageAdjustment", 0] }
                ]
            }
        }
    },
    {
        $addFields: {
            isCorrect: {
                $lte: [
                    { $abs: { $subtract: ["$adjustedBaseSalary", "$expectedBaseSalary"] } },
                    0.01
                ]
            }
        }
    },
    {
        $match: { isCorrect: false }
    }
]);

// 如果这个查询返回空结果，说明所有基本工资都已修复正确
```

### 3. 前端验证

修复数据库后，前端应该自动显示正确的数据：

1. **刷新浏览器页面**
2. **检查SalaryList**：基本工资列应显示原始值（如4200而不是3360）
3. **检查SalaryForm**：计算结果中基本工资应显示原始值
4. **检查SalaryDetail**：薪资详情中基本工资应显示原始值

## 预期修复结果

### 修复前（错误）
```
试用期员工：
- 基本工资显示：¥3,360 (错误的80%值)
- 应发工资显示：¥5,360
```

### 修复后（正确）
```
试用期员工：
- 基本工资显示：¥4,200 (正确的原始值)
- 应发工资显示：¥5,360 (80%应用于总薪资)
```

## 关键验证点

修复完成后，请验证以下几点：

1. **基本工资显示原始值**：
   - 试用期员工基本工资：¥4,200（不是¥3,360）
   - 正式员工基本工资：¥4,200（相同）

2. **应发工资计算正确**：
   - 试用期员工应发工资：¥5,360（80%）
   - 正式员工应发工资：¥6,700（100%）

3. **试用期说明显示**：
   - 应发工资旁边显示"(试用期工资为正常工资的80%)"

4. **状态同步正确**：
   - 保存后再打开仍然显示试用期说明

## 紧急修复命令

如果需要立即修复，可以运行以下命令：

```bash
# 1. 连接到MongoDB
mongo mchrms

# 2. 运行修复脚本
db.salaries.find().forEach(function(salary) {
    var correctBaseSalary = 3500 + (salary.educationAdjustment || 0) + (salary.languageAdjustment || 0);
    if (Math.abs(salary.adjustedBaseSalary - correctBaseSalary) > 0.01) {
        db.salaries.updateOne(
            { _id: salary._id },
            { $set: { adjustedBaseSalary: correctBaseSalary, originalBaseSalary: correctBaseSalary } }
        );
        print("修复: " + salary.employeeId + " " + salary.adjustedBaseSalary + " → " + correctBaseSalary);
    }
});

# 3. 退出MongoDB
exit
```

## 总结

- **问题根源**：数据库中保存了错误的基本工资数据（80%后的值）
- **修复方法**：重新计算并更新所有员工的基本工资为原始值
- **验证标准**：基本工资 = 3500 + 学历调整 + 语言调整
- **关键原则**：基本工资必须显示原始值，试用期80%只应用于最终应发工资

修复完成后，所有薪资管理相关文件的基本工资显示问题都将得到解决。
