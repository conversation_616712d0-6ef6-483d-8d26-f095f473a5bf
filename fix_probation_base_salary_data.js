// 修复数据库中被错误保存的试用期基本工资数据
const mongoose = require('mongoose');
const Salary = require('./backend/models/Salary');
const { SALARY_CONFIG } = require('./backend/config/salaryConfig');

// 连接数据库
async function connectDB() {
    try {
        await mongoose.connect('mongodb://localhost:27017/mchrms', {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('数据库连接成功');
    } catch (error) {
        console.error('数据库连接失败:', error);
        process.exit(1);
    }
}

// 修复试用期基本工资数据
async function fixProbationBaseSalaryData() {
    console.log('开始修复试用期基本工资数据...\n');

    try {
        // 获取所有薪资数据
        const salaries = await Salary.find({});
        console.log(`找到 ${salaries.length} 条薪资记录`);

        let fixedCount = 0;
        let probationCount = 0;
        let errorCount = 0;

        for (const salary of salaries) {
            try {
                const employeeId = salary.employeeId;
                const name = salary.name;
                const isProbation = salary.isProbation || salary.calculationResult?.isProbation || false;
                
                if (isProbation) {
                    probationCount++;
                }

                console.log(`\n处理员工: ${employeeId} - ${name} (试用期: ${isProbation})`);

                // 获取当前保存的基本工资相关数据
                const currentBaseSalary = Number(salary.adjustedBaseSalary) || 0;
                const educationAdjustment = Number(salary.educationAdjustment) || 0;
                const languageAdjustment = Number(salary.languageAdjustment) || 0;
                const educationCoefficient = Number(salary.educationCoefficient) || 1.0;
                const languageCoefficient = Number(salary.languageCoefficient) || 1.0;

                console.log('当前数据:');
                console.log('- 基本工资:', currentBaseSalary);
                console.log('- 学历调整:', educationAdjustment);
                console.log('- 语言调整:', languageAdjustment);
                console.log('- 学历系数:', educationCoefficient);
                console.log('- 语言系数:', languageCoefficient);

                // 计算正确的基本工资（不应用试用期系数）
                const baseSalary = SALARY_CONFIG.BASE_SALARY || 3500;
                let correctBaseSalary = baseSalary + educationAdjustment + languageAdjustment;
                correctBaseSalary = Math.round((correctBaseSalary + Number.EPSILON) * 100) / 100;

                console.log('计算的正确基本工资:', correctBaseSalary);

                // 检查是否需要修复
                const needsFix = Math.abs(currentBaseSalary - correctBaseSalary) > 0.01;

                if (needsFix) {
                    console.log(`❌ 需要修复: ${currentBaseSalary} → ${correctBaseSalary}`);

                    // 如果是试用期员工，检查是否基本工资被错误地应用了80%
                    if (isProbation) {
                        const possibleOriginalBaseSalary = Math.round((currentBaseSalary / 0.8 + Number.EPSILON) * 100) / 100;
                        console.log('可能的原始基本工资 (当前值/0.8):', possibleOriginalBaseSalary);
                        
                        // 如果除以0.8后的值接近正确值，说明确实被错误应用了80%
                        if (Math.abs(possibleOriginalBaseSalary - correctBaseSalary) < 0.01) {
                            console.log('✓ 确认基本工资被错误应用了80%系数');
                        }
                    }

                    // 更新数据库
                    const updateResult = await Salary.updateOne(
                        { _id: salary._id },
                        {
                            $set: {
                                adjustedBaseSalary: correctBaseSalary,
                                originalBaseSalary: correctBaseSalary
                            }
                        }
                    );

                    if (updateResult.modifiedCount > 0) {
                        console.log('✅ 修复成功');
                        fixedCount++;
                    } else {
                        console.log('⚠️ 修复失败');
                        errorCount++;
                    }
                } else {
                    console.log('✓ 数据正确，无需修复');
                }

            } catch (error) {
                console.error(`处理员工 ${salary.employeeId} 时出错:`, error);
                errorCount++;
            }
        }

        console.log('\n===== 修复总结 =====');
        console.log(`总记录数: ${salaries.length}`);
        console.log(`试用期员工数: ${probationCount}`);
        console.log(`修复成功数: ${fixedCount}`);
        console.log(`错误数: ${errorCount}`);
        console.log(`无需修复数: ${salaries.length - fixedCount - errorCount}`);

        if (fixedCount > 0) {
            console.log('\n✅ 基本工资数据修复完成！');
            console.log('修复内容:');
            console.log('- 移除了基本工资上错误应用的试用期80%系数');
            console.log('- 确保基本工资显示原始值');
            console.log('- 试用期80%只应用于最终的应发工资');
        } else {
            console.log('\n✓ 所有数据都是正确的，无需修复');
        }

    } catch (error) {
        console.error('修复过程中出错:', error);
    }
}

// 验证修复结果
async function verifyFixResults() {
    console.log('\n开始验证修复结果...');

    try {
        const probationEmployees = await Salary.find({
            $or: [
                { isProbation: true },
                { 'calculationResult.isProbation': true }
            ]
        });

        console.log(`\n找到 ${probationEmployees.length} 个试用期员工记录`);

        for (const employee of probationEmployees) {
            const baseSalary = SALARY_CONFIG.BASE_SALARY || 3500;
            const educationAdjustment = Number(employee.educationAdjustment) || 0;
            const languageAdjustment = Number(employee.languageAdjustment) || 0;
            
            let expectedBaseSalary = baseSalary + educationAdjustment + languageAdjustment;
            expectedBaseSalary = Math.round((expectedBaseSalary + Number.EPSILON) * 100) / 100;
            
            const actualBaseSalary = Number(employee.adjustedBaseSalary);
            const isCorrect = Math.abs(actualBaseSalary - expectedBaseSalary) < 0.01;

            console.log(`${employee.employeeId} - ${employee.name}:`);
            console.log(`  期望基本工资: ${expectedBaseSalary}`);
            console.log(`  实际基本工资: ${actualBaseSalary}`);
            console.log(`  是否正确: ${isCorrect ? '✓' : '✗'}`);
        }

    } catch (error) {
        console.error('验证过程中出错:', error);
    }
}

// 主函数
async function main() {
    await connectDB();
    
    console.log('试用期基本工资数据修复工具');
    console.log('================================');
    console.log('此工具将修复数据库中被错误应用试用期80%系数的基本工资数据');
    console.log('修复原则:');
    console.log('1. 基本工资应该显示原始值，不应用试用期80%系数');
    console.log('2. 试用期80%只应用于最终的应发工资');
    console.log('3. 各项薪资组成部分（基本工资、岗位工资、津贴等）都显示原始值');
    console.log('================================\n');

    await fixProbationBaseSalaryData();
    await verifyFixResults();

    console.log('\n修复完成！请重新加载前端页面查看修复结果。');
    
    // 关闭数据库连接
    await mongoose.connection.close();
    console.log('数据库连接已关闭');
}

// 运行修复
main().catch(error => {
    console.error('修复过程出错:', error);
    process.exit(1);
});
