#!/bin/bash

check_api_paths() {
    echo "=== 全面API路径检查 ==="
    
    # 1. 检查前端API调用
    echo -e "\n[1. 前端API调用检查]"
    echo "Fetch调用:"
    grep -rn "fetch(" frontend/src --include="*.js" | grep -E "http://|https://|apiBaseUrl"
    echo -e "\nAxios调用:"
    grep -rn "axios." frontend/src --include="*.js" | grep -E "http://|https://|apiBaseUrl"
    
    # 2. 检查后端路由
    echo -e "\n[2. 后端路由检查]"
    grep -rn -A2 "app.use" backend/server.js | grep -E "api|router"
    
    # 3. 检查端点定义
    echo -e "\n[3. 端点定义检查]"
    echo "健康检查端点:"
    grep -rn "/health" frontend/src backend
    echo -e "\nAPI版本端点:"
    grep -rn "/v[0-9]" frontend/src backend
    
    # 4. 检查配置
    echo -e "\n[4. 配置检查]"
    grep -rn "apiBaseUrl" frontend/src/config.js
    
    echo -e "\n=== 检查完成 ==="
}

check_api_paths | tee api_path_check_report_$(date +%Y%m%d).txt
echo "检查报告已保存到: api_path_check_report.txt"