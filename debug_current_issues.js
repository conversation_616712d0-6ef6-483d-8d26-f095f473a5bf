// 调试当前试用期问题
const { calculateMonthlySalary } = require('./backend/utils/SalaryCalculator');

// 模拟从数据库读取的试用期员工数据（可能被错误保存）
const savedProbationEmployeeData = {
    employeeId: 'M001',
    name: '张三',
    department: '工程部',
    subDepartment: '开发组',
    positionType: '技术',
    positionLevel: 'A5',
    education: '硕士（普通院校）',
    languageLevel: '精通',
    administrativeLevel: '无',
    performanceCoefficient: 1.0,
    actualAttendance: 22,
    specialAllowance: { amount: 0 },
    specialDeduction: { amount: 0 },
    // 试用期相关字段
    workType: '试用',
    probationEndDate: '2025-12-31',
    isProbation: true,
    // 可能被错误保存的基本工资（80%后的值）
    adjustedBaseSalary: 4480,  // 这可能是5600 * 0.8的结果
    originalBaseSalary: 4480,
    educationAdjustment: 1050,
    languageAdjustment: 1050,
    educationCoefficient: 1.3,
    languageCoefficient: 1.3,
    positionSalary: 1600,  // 这可能是2000 * 0.8的结果
    adminSalary: 0,
    performanceBonus: 0,
    calculationResult: {
        mealAllowance: 320,  // 这可能是400 * 0.8的结果
        communicationAllowance: 80,  // 这可能是100 * 0.8的结果
        totalMonthlySalary: 6480,  // 这是最终的80%结果
        socialInsurance: 515.13,
        tax: 344.87,
        netSalary: 5620,
        isProbation: true,
        probationFactor: 0.8
    }
};

// 模拟前端试用期判断逻辑
function isProbationPeriod(employeeData) {
    if (!employeeData) return false;
    
    // 如果工作类型是试用，直接返回true
    if (employeeData.workType === '试用') return true;
    
    // 如果有isProbation字段且为true，返回true
    if (employeeData.isProbation === true) return true;
    
    // 如果有试用期结束日期，检查当前日期是否在试用期内
    if (employeeData.probationEndDate) {
        const today = new Date();
        const probationEndDate = new Date(employeeData.probationEndDate);
        return today <= probationEndDate;
    }
    
    return false;
}

// 模拟processSalaryData函数
function processSalaryData(employeeData, config) {
    const processedData = { ...employeeData };
    
    // 确保试用期状态正确设置
    processedData.isProbation = isProbationPeriod(employeeData);
    
    return processedData;
}

function debugCurrentIssues() {
    console.log('开始调试当前试用期问题...\n');

    console.log('===== 问题1：数据库中可能保存了错误的80%数据 =====');
    
    console.log('从数据库读取的数据（可能有问题）:');
    console.log('- 基本工资:', savedProbationEmployeeData.adjustedBaseSalary);
    console.log('- 岗位工资:', savedProbationEmployeeData.positionSalary);
    console.log('- 餐补:', savedProbationEmployeeData.calculationResult.mealAllowance);
    console.log('- 通讯补贴:', savedProbationEmployeeData.calculationResult.communicationAllowance);
    console.log('- 应发工资:', savedProbationEmployeeData.calculationResult.totalMonthlySalary);

    // 重新计算正确的薪资
    console.log('\n重新计算的正确薪资:');
    const correctCalculation = calculateMonthlySalary({
        positionLevel: savedProbationEmployeeData.positionLevel,
        positionType: savedProbationEmployeeData.positionType,
        education: savedProbationEmployeeData.education,
        languageLevel: savedProbationEmployeeData.languageLevel,
        administrativeLevel: savedProbationEmployeeData.administrativeLevel,
        performanceCoefficient: savedProbationEmployeeData.performanceCoefficient,
        actualAttendance: savedProbationEmployeeData.actualAttendance,
        specialAllowance: savedProbationEmployeeData.specialAllowance,
        specialDeduction: savedProbationEmployeeData.specialDeduction,
        isProbation: savedProbationEmployeeData.isProbation,
        workType: savedProbationEmployeeData.workType,
        probationEndDate: savedProbationEmployeeData.probationEndDate
    });

    console.log('- 基本工资:', correctCalculation.adjustedBaseSalary);
    console.log('- 岗位工资:', correctCalculation.positionSalary);
    console.log('- 餐补:', correctCalculation.calculationResult.mealAllowance);
    console.log('- 通讯补贴:', correctCalculation.calculationResult.communicationAllowance);
    console.log('- 应发工资:', correctCalculation.calculationResult.totalMonthlySalary);

    // 检查数据是否有问题
    const baseSalaryWrong = Math.abs(savedProbationEmployeeData.adjustedBaseSalary - correctCalculation.adjustedBaseSalary) > 0.01;
    const positionSalaryWrong = Math.abs(savedProbationEmployeeData.positionSalary - correctCalculation.positionSalary) > 0.01;
    const mealAllowanceWrong = Math.abs(savedProbationEmployeeData.calculationResult.mealAllowance - correctCalculation.calculationResult.mealAllowance) > 0.01;
    const commAllowanceWrong = Math.abs(savedProbationEmployeeData.calculationResult.communicationAllowance - correctCalculation.calculationResult.communicationAllowance) > 0.01;

    console.log('\n数据问题检查:');
    console.log('- 基本工资是否错误:', baseSalaryWrong ? '✗ 错误' : '✓ 正确');
    console.log('- 岗位工资是否错误:', positionSalaryWrong ? '✗ 错误' : '✓ 正确');
    console.log('- 餐补是否错误:', mealAllowanceWrong ? '✗ 错误' : '✓ 正确');
    console.log('- 通讯补贴是否错误:', commAllowanceWrong ? '✗ 错误' : '✓ 正确');

    if (baseSalaryWrong || positionSalaryWrong || mealAllowanceWrong || commAllowanceWrong) {
        console.log('\n❌ 数据库中确实保存了错误的80%数据！');
        console.log('这解释了为什么前端显示的是80%的结果。');
    } else {
        console.log('\n✓ 数据库中的数据是正确的。');
    }

    console.log('\n===== 问题2：前端状态同步问题 =====');
    
    // 模拟SalaryForm.js的初始化
    console.log('SalaryForm.js 初始化:');
    const formProbationStatus = isProbationPeriod(savedProbationEmployeeData);
    console.log('- 试用期状态判断:', formProbationStatus);
    console.log('- 应该显示试用期说明:', formProbationStatus ? '是' : '否');

    // 模拟SalaryDetail.js的数据处理
    console.log('\nSalaryDetail.js 数据处理:');
    const processedData = processSalaryData(savedProbationEmployeeData, {});
    console.log('- 处理后的试用期状态:', processedData.isProbation);
    console.log('- 应该显示试用期说明:', processedData.isProbation ? '是' : '否');

    console.log('\n===== 问题3：显示逻辑检查 =====');
    
    console.log('SalaryList.js 应该显示:');
    console.log('- 基本工资:', savedProbationEmployeeData.adjustedBaseSalary, '(无试用期标识)');
    console.log('- 工作状态: 试用期');
    console.log('- 应发工资:', savedProbationEmployeeData.calculationResult.totalMonthlySalary);

    console.log('\nSalaryForm.js 应该显示:');
    console.log('- 基本工资:', savedProbationEmployeeData.adjustedBaseSalary);
    console.log('- 岗位工资:', savedProbationEmployeeData.positionSalary);
    console.log('- 应发工资:', savedProbationEmployeeData.calculationResult.totalMonthlySalary, '+ 试用期说明');

    console.log('\nSalaryDetail.js 应该显示:');
    console.log('- 基本工资:', savedProbationEmployeeData.adjustedBaseSalary);
    console.log('- 岗位工资:', savedProbationEmployeeData.positionSalary);
    console.log('- 应发工资:', savedProbationEmployeeData.calculationResult.totalMonthlySalary, '+ 试用期说明');

    console.log('\n===== 解决方案 =====');
    
    if (baseSalaryWrong || positionSalaryWrong || mealAllowanceWrong || commAllowanceWrong) {
        console.log('1. 数据库修复：需要运行修复脚本，将错误的80%数据恢复为原始值');
        console.log('2. 重新计算：对所有试用期员工重新计算薪资');
        console.log('3. 前端验证：确保前端显示逻辑正确');
    } else {
        console.log('1. 检查前端状态同步逻辑');
        console.log('2. 检查试用期判断函数');
        console.log('3. 检查显示组件的条件判断');
    }

    console.log('\n===== 立即可执行的修复步骤 =====');
    console.log('1. 检查数据库中实际的试用期员工数据');
    console.log('2. 如果数据错误，运行数据修复脚本');
    console.log('3. 重新加载前端页面验证显示效果');
    console.log('4. 测试保存后再打开的功能');
}

// 运行调试
debugCurrentIssues();
