// 强制修复数据库中的基本工资数据
const { calculateMonthlySalary } = require('./backend/utils/SalaryCalculator');

// 模拟数据库修复（由于无法连接数据库，我们创建一个模拟的修复逻辑）
function simulateDatabaseFix() {
    console.log('模拟数据库基本工资修复...\n');

    // 模拟一些可能存在问题的数据
    const problematicData = [
        {
            employeeId: 'M001',
            name: '张三',
            isProbation: true,
            workType: '试用',
            education: '本科（普通院校）',
            languageLevel: '熟练',
            // 假设这是错误保存的80%基本工资
            savedBaseSalary: 3360, // 4200 * 0.8 = 3360 (错误)
            correctBaseSalary: 4200 // 3500 + 700 = 4200 (正确)
        },
        {
            employeeId: 'M002',
            name: '李四',
            isProbation: true,
            workType: '试用',
            education: '硕士（普通院校）',
            languageLevel: '精通',
            // 假设这是错误保存的80%基本工资
            savedBaseSalary: 4480, // 5600 * 0.8 = 4480 (错误)
            correctBaseSalary: 5600 // 3500 + 0 + 2100 = 5600 (正确)
        },
        {
            employeeId: 'M003',
            name: '王五',
            isProbation: false,
            workType: '全职',
            education: '本科（普通院校）',
            languageLevel: '熟练',
            // 正式员工的基本工资应该是正确的
            savedBaseSalary: 4200,
            correctBaseSalary: 4200
        }
    ];

    console.log('检查数据库中的基本工资数据:');
    console.log('=' * 60);

    let fixedCount = 0;
    let totalCount = 0;

    for (const employee of problematicData) {
        totalCount++;
        console.log(`\n员工: ${employee.employeeId} - ${employee.name}`);
        console.log(`试用期状态: ${employee.isProbation}`);
        console.log(`保存的基本工资: ${employee.savedBaseSalary}`);
        console.log(`正确的基本工资: ${employee.correctBaseSalary}`);

        const needsFix = Math.abs(employee.savedBaseSalary - employee.correctBaseSalary) > 0.01;

        if (needsFix) {
            console.log(`❌ 需要修复: ${employee.savedBaseSalary} → ${employee.correctBaseSalary}`);
            
            if (employee.isProbation) {
                // 检查是否是80%的问题
                const possibleOriginal = Math.round((employee.savedBaseSalary / 0.8 + Number.EPSILON) * 100) / 100;
                if (Math.abs(possibleOriginal - employee.correctBaseSalary) < 0.01) {
                    console.log(`🔍 确认: 基本工资被错误地应用了80%系数`);
                }
            }
            
            // 模拟修复
            console.log(`✅ 已修复: 基本工资更新为 ${employee.correctBaseSalary}`);
            fixedCount++;
        } else {
            console.log(`✓ 数据正确，无需修复`);
        }
    }

    console.log('\n' + '=' * 60);
    console.log('修复总结:');
    console.log(`总记录数: ${totalCount}`);
    console.log(`修复成功数: ${fixedCount}`);
    console.log(`无需修复数: ${totalCount - fixedCount}`);

    return { totalCount, fixedCount };
}

// 生成实际的数据库修复SQL/MongoDB命令
function generateDatabaseFixCommands() {
    console.log('\n=== 生成数据库修复命令 ===\n');

    const commands = `
-- 如果使用MongoDB，可以使用以下命令修复基本工资数据

// 1. 查找所有试用期员工的薪资记录
db.salaries.find({
    $or: [
        { isProbation: true },
        { workType: "试用" },
        { "calculationResult.isProbation": true }
    ]
});

// 2. 修复基本工资数据的示例命令
// 注意：这需要根据实际的学历和语言调整值来计算正确的基本工资

// 对于学历为"本科（普通院校）"，语言为"熟练"的员工
// 正确的基本工资应该是: 3500 + 0 + 700 = 4200
db.salaries.updateMany(
    {
        education: "本科（普通院校）",
        languageLevel: "熟练",
        adjustedBaseSalary: 3360  // 错误的80%值
    },
    {
        $set: {
            adjustedBaseSalary: 4200,  // 正确的原始值
            originalBaseSalary: 4200
        }
    }
);

// 对于学历为"硕士（普通院校）"，语言为"精通"的员工
// 正确的基本工资应该是: 3500 + 0 + 2100 = 5600
db.salaries.updateMany(
    {
        education: "硕士（普通院校）",
        languageLevel: "精通",
        adjustedBaseSalary: 4480  // 错误的80%值
    },
    {
        $set: {
            adjustedBaseSalary: 5600,  // 正确的原始值
            originalBaseSalary: 5600
        }
    }
);

// 3. 通用修复脚本（需要根据实际情况调整）
// 这个脚本会重新计算所有员工的基本工资
db.salaries.find().forEach(function(salary) {
    // 计算正确的基本工资
    var baseSalary = 3500;
    var educationAdjustment = salary.educationAdjustment || 0;
    var languageAdjustment = salary.languageAdjustment || 0;
    var correctBaseSalary = baseSalary + educationAdjustment + languageAdjustment;
    
    // 如果当前保存的基本工资不正确，则更新
    if (Math.abs(salary.adjustedBaseSalary - correctBaseSalary) > 0.01) {
        db.salaries.updateOne(
            { _id: salary._id },
            {
                $set: {
                    adjustedBaseSalary: correctBaseSalary,
                    originalBaseSalary: correctBaseSalary
                }
            }
        );
        print("修复员工 " + salary.employeeId + " 的基本工资: " + 
              salary.adjustedBaseSalary + " → " + correctBaseSalary);
    }
});

// 4. 验证修复结果
db.salaries.aggregate([
    {
        $project: {
            employeeId: 1,
            name: 1,
            isProbation: 1,
            adjustedBaseSalary: 1,
            educationAdjustment: 1,
            languageAdjustment: 1,
            calculatedBaseSalary: {
                $add: [
                    3500,
                    { $ifNull: ["$educationAdjustment", 0] },
                    { $ifNull: ["$languageAdjustment", 0] }
                ]
            },
            isCorrect: {
                $eq: [
                    "$adjustedBaseSalary",
                    {
                        $add: [
                            3500,
                            { $ifNull: ["$educationAdjustment", 0] },
                            { $ifNull: ["$languageAdjustment", 0] }
                        ]
                    }
                ]
            }
        }
    },
    {
        $match: { isCorrect: false }
    }
]);
`;

    console.log(commands);
}

// 生成前端修复指导
function generateFrontendFixGuide() {
    console.log('\n=== 前端修复指导 ===\n');

    const guide = `
前端修复检查清单:

1. ✅ SalaryList.js - 基本工资列
   - 已移除试用期标识
   - 只显示基本工资数值

2. ✅ SalaryForm.js - 薪资计算结果显示
   - 基本工资显示原始值
   - 应发工资显示80%后的值（试用期）
   - 添加了试用期说明

3. ✅ SalaryDetail.js - 薪资详情显示
   - 基本工资显示原始值
   - 应发工资显示80%后的值（试用期）
   - 添加了试用期说明
   - 社保缴费基数显示配置的固定值

4. ✅ 前端状态同步
   - 修复了试用期状态判断逻辑
   - 修复了保存后再打开的状态恢复

5. ⚠️ 需要验证的关键点:
   - 确保所有基本工资显示都是原始值
   - 确保试用期说明正确显示
   - 确保保存的数据是正确的

测试步骤:
1. 打开试用期员工的薪资计算页面
2. 点击"计算薪资"，检查基本工资是否显示原始值
3. 检查应发工资是否显示80%后的值并有说明
4. 保存数据
5. 关闭后重新打开，检查数据是否正确
6. 进入薪资详情页面，检查所有显示是否正确
`;

    console.log(guide);
}

// 主函数
function main() {
    console.log('强制修复数据库基本工资数据');
    console.log('================================\n');

    // 1. 模拟数据库修复
    const { totalCount, fixedCount } = simulateDatabaseFix();

    // 2. 生成实际的数据库修复命令
    generateDatabaseFixCommands();

    // 3. 生成前端修复指导
    generateFrontendFixGuide();

    console.log('\n=== 总结 ===');
    console.log('问题根源: 数据库中保存了错误的基本工资数据（80%后的值）');
    console.log('解决方案: 重新计算并更新所有员工的基本工资为原始值');
    console.log('验证方法: 确保基本工资 = 3500 + 学历调整 + 语言调整');
    console.log('\n关键原则:');
    console.log('❗ 基本工资必须保存和显示原始值');
    console.log('❗ 试用期80%只应用于最终应发工资');
    console.log('❗ 前端显示必须与数据库保存的数据一致');
}

// 运行修复
main();
