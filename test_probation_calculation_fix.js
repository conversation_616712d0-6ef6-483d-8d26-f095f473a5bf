// 测试试用期计算逻辑修复
const { calculateMonthlySalary } = require('./backend/utils/SalaryCalculator');

// 测试函数
function testProbationCalculationFix() {
    console.log('开始测试试用期计算逻辑修复...\n');

    // 测试用例：试用期员工
    const probationEmployee = {
        employeeId: 'TEST001',
        name: '试用期员工',
        department: '工程部',
        subDepartment: '开发组',
        positionType: '技术',
        positionLevel: 'A5',
        education: '本科（普通院校）',
        languageLevel: '熟练',
        administrativeLevel: '无',
        performanceCoefficient: 1.0,
        actualAttendance: 22,
        specialAllowance: { amount: 0 },
        specialDeduction: { amount: 0 },
        // 试用期相关字段
        workType: '试用',
        probationEndDate: '2025-12-31',
        isProbation: true
    };

    // 测试用例：正式员工
    const regularEmployee = {
        ...probationEmployee,
        name: '正式员工',
        employeeId: 'TEST002',
        workType: '全职',
        probationEndDate: '',
        isProbation: false
    };

    console.log('===== 试用期员工计算验证 =====');
    const probationResult = calculateMonthlySalary(probationEmployee);
    
    console.log('试用期员工薪资组成（应显示原始值）:');
    console.log('- 基本工资:', probationResult.adjustedBaseSalary);
    console.log('- 岗位工资:', probationResult.positionSalary);
    console.log('- 管理津贴:', probationResult.adminSalary);
    console.log('- 绩效奖金:', probationResult.performanceBonus);
    console.log('- 餐补:', probationResult.calculationResult.mealAllowance);
    console.log('- 通讯补贴:', probationResult.calculationResult.communicationAllowance);
    
    console.log('\n试用期员工薪资计算结果:');
    console.log('- 原始总薪资:', probationResult.calculationResult.originalTotalSalary);
    console.log('- 应发工资 (80%):', probationResult.calculationResult.totalMonthlySalary);
    console.log('- 试用期系数:', probationResult.probationFactor);
    console.log('- 社保:', probationResult.calculationResult.socialInsurance);
    console.log('- 个税:', probationResult.calculationResult.tax);
    console.log('- 实发工资:', probationResult.calculationResult.netSalary);

    console.log('\n===== 正式员工计算验证 =====');
    const regularResult = calculateMonthlySalary(regularEmployee);
    
    console.log('正式员工薪资组成:');
    console.log('- 基本工资:', regularResult.adjustedBaseSalary);
    console.log('- 岗位工资:', regularResult.positionSalary);
    console.log('- 管理津贴:', regularResult.adminSalary);
    console.log('- 绩效奖金:', regularResult.performanceBonus);
    console.log('- 餐补:', regularResult.calculationResult.mealAllowance);
    console.log('- 通讯补贴:', regularResult.calculationResult.communicationAllowance);
    
    console.log('\n正式员工薪资计算结果:');
    console.log('- 应发工资:', regularResult.calculationResult.totalMonthlySalary);
    console.log('- 试用期系数:', regularResult.probationFactor);
    console.log('- 社保:', regularResult.calculationResult.socialInsurance);
    console.log('- 个税:', regularResult.calculationResult.tax);
    console.log('- 实发工资:', regularResult.calculationResult.netSalary);

    console.log('\n===== 计算逻辑验证 =====');
    
    // 验证基本工资是否相同（试用期不应影响基本工资）
    const baseSalaryEqual = probationResult.adjustedBaseSalary === regularResult.adjustedBaseSalary;
    console.log('基本工资是否相同:', baseSalaryEqual ? '✓ 正确' : '✗ 错误');
    console.log('- 试用期员工基本工资:', probationResult.adjustedBaseSalary);
    console.log('- 正式员工基本工资:', regularResult.adjustedBaseSalary);
    
    // 验证岗位工资是否相同
    const positionSalaryEqual = probationResult.positionSalary === regularResult.positionSalary;
    console.log('\n岗位工资是否相同:', positionSalaryEqual ? '✓ 正确' : '✗ 错误');
    console.log('- 试用期员工岗位工资:', probationResult.positionSalary);
    console.log('- 正式员工岗位工资:', regularResult.positionSalary);
    
    // 验证津贴是否相同
    const allowanceEqual = (
        probationResult.calculationResult.mealAllowance === regularResult.calculationResult.mealAllowance &&
        probationResult.calculationResult.communicationAllowance === regularResult.calculationResult.communicationAllowance
    );
    console.log('\n津贴是否相同:', allowanceEqual ? '✓ 正确' : '✗ 错误');
    console.log('- 试用期员工餐补:', probationResult.calculationResult.mealAllowance);
    console.log('- 正式员工餐补:', regularResult.calculationResult.mealAllowance);
    console.log('- 试用期员工通讯补贴:', probationResult.calculationResult.communicationAllowance);
    console.log('- 正式员工通讯补贴:', regularResult.calculationResult.communicationAllowance);
    
    // 验证试用期80%计算
    const expectedProbationSalary = Math.round(regularResult.calculationResult.totalMonthlySalary * 0.8);
    const probationCalculationCorrect = Math.abs(probationResult.calculationResult.totalMonthlySalary - expectedProbationSalary) < 1;
    console.log('\n试用期80%计算是否正确:', probationCalculationCorrect ? '✓ 正确' : '✗ 错误');
    console.log('- 正式员工应发工资:', regularResult.calculationResult.totalMonthlySalary);
    console.log('- 期望试用期应发工资 (80%):', expectedProbationSalary);
    console.log('- 实际试用期应发工资:', probationResult.calculationResult.totalMonthlySalary);
    
    // 验证原始总薪资字段
    const originalTotalSalaryCorrect = probationResult.calculationResult.originalTotalSalary === regularResult.calculationResult.totalMonthlySalary;
    console.log('\n原始总薪资字段是否正确:', originalTotalSalaryCorrect ? '✓ 正确' : '✗ 错误');
    console.log('- 试用期员工原始总薪资:', probationResult.calculationResult.originalTotalSalary);
    console.log('- 正式员工应发工资:', regularResult.calculationResult.totalMonthlySalary);

    console.log('\n===== 修复验证总结 =====');
    
    const allCorrect = baseSalaryEqual && positionSalaryEqual && allowanceEqual && probationCalculationCorrect && originalTotalSalaryCorrect;
    
    if (allCorrect) {
        console.log('✅ 试用期计算逻辑修复成功！');
        console.log('\n正确的计算逻辑:');
        console.log('1. 基本工资、岗位工资、管理津贴、绩效奖金、餐补、通讯补贴 → 显示原始值');
        console.log('2. 税前应发工资 = (所有薪资组成部分之和) × 80% (仅试用期)');
        console.log('3. 社保和个税基于80%后的应发工资计算');
        console.log('4. 保存originalTotalSalary字段用于说明显示');
    } else {
        console.log('❌ 试用期计算逻辑仍有问题！');
        console.log('\n问题检查:');
        console.log('- 基本工资相同:', baseSalaryEqual ? '✓' : '✗');
        console.log('- 岗位工资相同:', positionSalaryEqual ? '✓' : '✗');
        console.log('- 津贴相同:', allowanceEqual ? '✓' : '✗');
        console.log('- 试用期80%计算:', probationCalculationCorrect ? '✓' : '✗');
        console.log('- 原始总薪资字段:', originalTotalSalaryCorrect ? '✓' : '✗');
    }
    
    console.log('\n前端显示验证:');
    console.log('- 试用期员工应发工资旁边应显示: "(试用期工资为正常工资的80%)"');
    console.log('- 各项薪资组成部分应显示原始值，不显示80%后的值');
    console.log('- 只有最终的应发工资显示80%后的值');
}

// 运行测试
testProbationCalculationFix();
