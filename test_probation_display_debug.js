// 测试试用期显示问题调试
const { calculateMonthlySalary } = require('./backend/utils/SalaryCalculator');

// 模拟前端试用期判断逻辑
function isProbationPeriod(employee) {
    if (!employee) return false;
    
    // 检查多个可能的试用期标识
    const isProbationFlag = employee.isProbation === true;
    const workTypeCheck = employee.workType === '试用';
    const probationEndDateCheck = employee.probationEndDate && new Date(employee.probationEndDate) > new Date();
    
    console.log('试用期判断详情:', {
        isProbationFlag,
        workTypeCheck,
        probationEndDateCheck,
        isProbation: employee.isProbation,
        workType: employee.workType,
        probationEndDate: employee.probationEndDate
    });
    
    return isProbationFlag || workTypeCheck || probationEndDateCheck;
}

// 测试函数
function testProbationDisplayDebug() {
    console.log('开始调试试用期显示问题...\n');

    // 测试用例1：明确的试用期员工
    const probationEmployee1 = {
        employeeId: 'TEST001',
        name: '试用期员工1',
        department: '工程部',
        subDepartment: '开发组',
        positionType: '技术',
        positionLevel: 'A5',
        education: '本科（普通院校）',
        languageLevel: '熟练',
        administrativeLevel: '无',
        performanceCoefficient: 1.0,
        actualAttendance: 22,
        specialAllowance: { amount: 0 },
        specialDeduction: { amount: 0 },
        // 试用期相关字段
        workType: '试用',
        probationEndDate: '2025-12-31',
        isProbation: true
    };

    // 测试用例2：只有workType的试用期员工
    const probationEmployee2 = {
        ...probationEmployee1,
        name: '试用期员工2',
        employeeId: 'TEST002',
        isProbation: false, // 故意设为false
        workType: '试用'
    };

    // 测试用例3：只有isProbation的试用期员工
    const probationEmployee3 = {
        ...probationEmployee1,
        name: '试用期员工3',
        employeeId: 'TEST003',
        isProbation: true,
        workType: '全职' // 故意设为全职
    };

    // 测试用例4：正式员工
    const regularEmployee = {
        ...probationEmployee1,
        name: '正式员工',
        employeeId: 'TEST004',
        workType: '全职',
        probationEndDate: '',
        isProbation: false
    };

    const testCases = [
        { name: '试用期员工1（完整标识）', employee: probationEmployee1 },
        { name: '试用期员工2（仅workType）', employee: probationEmployee2 },
        { name: '试用期员工3（仅isProbation）', employee: probationEmployee3 },
        { name: '正式员工', employee: regularEmployee }
    ];

    testCases.forEach((testCase, index) => {
        console.log(`\n===== 测试用例${index + 1}: ${testCase.name} =====`);
        
        const employee = testCase.employee;
        console.log('员工信息:');
        console.log('- isProbation:', employee.isProbation);
        console.log('- workType:', employee.workType);
        console.log('- probationEndDate:', employee.probationEndDate);
        
        // 前端试用期判断
        const frontendIsProbation = isProbationPeriod(employee);
        console.log('前端试用期判断结果:', frontendIsProbation);
        
        // 后端计算
        console.log('\n后端计算结果:');
        const salaryResult = calculateMonthlySalary(employee);
        console.log('- 后端isProbation:', salaryResult.isProbation);
        console.log('- 后端probationFactor:', salaryResult.probationFactor);
        console.log('- 应发工资:', salaryResult.calculationResult.totalMonthlySalary);
        console.log('- 原始总薪资:', salaryResult.calculationResult.originalTotalSalary || '无');
        
        // 验证一致性
        console.log('\n一致性检查:');
        const isConsistent = frontendIsProbation === salaryResult.isProbation;
        console.log('前后端试用期判断是否一致:', isConsistent ? '✓ 一致' : '✗ 不一致');
        
        if (!isConsistent) {
            console.log('❌ 不一致！这可能导致前端显示问题');
            console.log('- 前端判断:', frontendIsProbation);
            console.log('- 后端判断:', salaryResult.isProbation);
        }
        
        // 模拟前端显示逻辑
        console.log('\n前端显示模拟:');
        if (frontendIsProbation) {
            console.log('✓ 应该显示试用期说明: "(试用期工资为正常工资的80%)"');
        } else {
            console.log('- 不显示试用期说明');
        }
        
        console.log('-'.repeat(60));
    });

    console.log('\n===== 问题诊断 =====');
    console.log('可能的问题原因:');
    console.log('1. 前端试用期判断逻辑与后端不一致');
    console.log('2. 前端组件中的isProbation状态没有正确传递');
    console.log('3. 前端组件中的条件判断有问题');
    console.log('4. 数据传递过程中丢失了试用期标识');
    
    console.log('\n建议检查:');
    console.log('1. SalaryForm.js中的isProbation状态来源');
    console.log('2. SalaryDetail.js中的processedEmployee.isProbation值');
    console.log('3. 数据处理函数中是否正确保留了试用期标识');
    console.log('4. 前端试用期判断逻辑是否与后端一致');
}

// 运行测试
testProbationDisplayDebug();
