// 测试前端试用期薪资显示修复
const { generateSalaryFormula } = require('./frontend/src/utils/salaryUtils');

// 模拟薪资配置
const mockConfig = {
    SALARY_CONFIG: {
        BASE_SALARY: 3500,
        MEAL_ALLOWANCE: 200,
        COMMUNICATION_ALLOWANCE: 100
    }
};

// 测试函数
function testFrontendProbationFix() {
    console.log('开始测试前端试用期薪资显示修复...\n');

    // 测试用例1：试用期员工
    const probationEmployee = {
        employeeId: 'M001',
        name: '张三',
        adjustedBaseSalary: 4550, // 试用期调整后的基本工资
        originalBaseSalary: 5687.5, // 原始基本工资（未应用试用期系数）
        educationAdjustment: 1050,
        languageAdjustment: 1137.5,
        educationCoefficient: 1.3,
        languageCoefficient: 1.325,
        isProbation: true,
        workType: '试用',
        probationFactor: 0.8
    };

    // 测试用例2：正式员工
    const regularEmployee = {
        employeeId: 'M002',
        name: '李四',
        adjustedBaseSalary: 5687.5,
        originalBaseSalary: 5687.5,
        educationAdjustment: 1050,
        languageAdjustment: 1137.5,
        educationCoefficient: 1.3,
        languageCoefficient: 1.325,
        isProbation: false,
        workType: '全职',
        probationFactor: 1.0
    };

    // 测试用例3：薪资重置的员工
    const resetEmployee = {
        employeeId: 'M003',
        name: '王五',
        adjustedBaseSalary: 0,
        originalBaseSalary: 0,
        educationAdjustment: 0,
        languageAdjustment: 0,
        educationCoefficient: 1.0,
        languageCoefficient: 1.0,
        isProbation: false,
        workType: '全职',
        probationFactor: 1.0
    };

    console.log('===== 测试基本工资计算公式显示 =====');
    
    console.log('\n1. 试用期员工基本工资公式:');
    const probationFormula = generateSalaryFormula(probationEmployee, mockConfig);
    console.log('公式:', probationFormula);
    console.log('是否包含"× 80%":', probationFormula.includes('× 80%') ? '✗ 错误（不应包含）' : '✓ 正确');
    
    console.log('\n2. 正式员工基本工资公式:');
    const regularFormula = generateSalaryFormula(regularEmployee, mockConfig);
    console.log('公式:', regularFormula);
    console.log('是否包含"× 80%":', regularFormula.includes('× 80%') ? '✗ 错误（不应包含）' : '✓ 正确');
    
    console.log('\n3. 薪资重置员工基本工资公式:');
    const resetFormula = generateSalaryFormula(resetEmployee, mockConfig);
    console.log('公式:', resetFormula);
    console.log('是否显示重置信息:', resetFormula.includes('已重置为 0') ? '✓ 正确' : '✗ 错误');

    console.log('\n===== 验证计算结果 =====');
    
    // 验证试用期员工的基本工资计算
    const expectedProbationBaseSalary = 3500 + 1050 + 1137.5; // 5687.5
    const actualProbationBaseSalary = expectedProbationBaseSalary * 0.8; // 4550
    
    console.log('\n试用期员工基本工资验证:');
    console.log('基本工资 (3500) + 学历调整 (1050) + 语言调整 (1137.5) = 5687.5');
    console.log('试用期调整: 5687.5 × 80% = 4550');
    console.log('实际显示的基本工资:', probationEmployee.adjustedBaseSalary);
    console.log('计算是否正确:', Math.abs(probationEmployee.adjustedBaseSalary - actualProbationBaseSalary) < 0.01 ? '✓ 正确' : '✗ 错误');

    console.log('\n===== 测试完成 =====');
    console.log('总结:');
    console.log('1. 试用期员工基本工资公式不再显示"× 80%"');
    console.log('2. 试用期员工的基本工资已经是调整后的值');
    console.log('3. 试用期说明会在薪资计算结果和薪资详情中单独显示');
}

// 运行测试
testFrontendProbationFix();
