// 测试最终的试用期薪资计算修复
const { calculateMonthlySalary } = require('./backend/utils/SalaryCalculator');

// 测试函数
function testFinalProbationFix() {
    console.log('开始测试最终的试用期薪资计算修复...\n');

    // 测试用例：试用期员工
    const probationEmployee = {
        employeeId: 'TEST001',
        name: '试用期员工',
        department: '工程部',
        subDepartment: '开发组',
        positionType: '技术',
        positionLevel: 'A5',
        education: '硕士（普通院校）', // 系数1.3
        languageLevel: '精通', // 系数1.3
        administrativeLevel: '无',
        performanceCoefficient: 1.0,
        actualAttendance: 22,
        specialAllowance: { amount: 0 },
        specialDeduction: { amount: 0 },
        // 试用期相关字段
        workType: '试用',
        probationEndDate: '2025-12-31',
        isProbation: true
    };

    // 相同条件的正式员工
    const regularEmployee = {
        ...probationEmployee,
        name: '正式员工',
        workType: '全职',
        probationEndDate: '',
        isProbation: false
    };

    console.log('测试员工信息:');
    console.log('- 学历:', probationEmployee.education, '(系数1.3)');
    console.log('- 语言水平:', probationEmployee.languageLevel, '(系数1.3)');
    console.log('- 岗位:', probationEmployee.positionType, probationEmployee.positionLevel);

    // 计算薪资
    console.log('\n计算试用期员工薪资...');
    const probationResult = calculateMonthlySalary(probationEmployee);

    console.log('\n计算正式员工薪资...');
    const regularResult = calculateMonthlySalary(regularEmployee);

    console.log('\n===== 薪资组成部分对比 =====');
    
    console.log('基本工资:');
    console.log(`  试用期员工: ${probationResult.adjustedBaseSalary}`);
    console.log(`  正式员工: ${regularResult.adjustedBaseSalary}`);
    console.log(`  是否相等: ${probationResult.adjustedBaseSalary === regularResult.adjustedBaseSalary ? '✓ 正确' : '✗ 错误'}`);

    console.log('\n岗位工资:');
    console.log(`  试用期员工: ${probationResult.positionSalary}`);
    console.log(`  正式员工: ${regularResult.positionSalary}`);
    console.log(`  是否相等: ${probationResult.positionSalary === regularResult.positionSalary ? '✓ 正确' : '✗ 错误'}`);

    console.log('\n管理津贴:');
    console.log(`  试用期员工: ${probationResult.adminSalary}`);
    console.log(`  正式员工: ${regularResult.adminSalary}`);
    console.log(`  是否相等: ${probationResult.adminSalary === regularResult.adminSalary ? '✓ 正确' : '✗ 错误'}`);

    console.log('\n绩效奖金:');
    console.log(`  试用期员工: ${probationResult.performanceBonus}`);
    console.log(`  正式员工: ${regularResult.performanceBonus}`);
    console.log(`  是否相等: ${probationResult.performanceBonus === regularResult.performanceBonus ? '✓ 正确' : '✗ 错误'}`);

    console.log('\n餐补:');
    console.log(`  试用期员工: ${probationResult.calculationResult.mealAllowance}`);
    console.log(`  正式员工: ${regularResult.calculationResult.mealAllowance}`);
    console.log(`  是否相等: ${probationResult.calculationResult.mealAllowance === regularResult.calculationResult.mealAllowance ? '✓ 正确' : '✗ 错误'}`);

    console.log('\n通讯补贴:');
    console.log(`  试用期员工: ${probationResult.calculationResult.communicationAllowance}`);
    console.log(`  正式员工: ${regularResult.calculationResult.communicationAllowance}`);
    console.log(`  是否相等: ${probationResult.calculationResult.communicationAllowance === regularResult.calculationResult.communicationAllowance ? '✓ 正确' : '✗ 错误'}`);

    console.log('\n===== 总薪资对比 =====');
    
    const probationTotal = probationResult.calculationResult.totalMonthlySalary;
    const regularTotal = regularResult.calculationResult.totalMonthlySalary;
    const expectedProbationTotal = Math.round(regularTotal * 0.8);
    
    console.log(`正式员工应发工资: ${regularTotal}`);
    console.log(`试用期员工应发工资: ${probationTotal}`);
    console.log(`期望的试用期应发工资 (80%): ${expectedProbationTotal}`);
    console.log(`80%计算是否正确: ${Math.abs(probationTotal - expectedProbationTotal) < 1 ? '✓ 正确' : '✗ 错误'}`);

    // 验证原始总薪资字段
    console.log('\n===== 原始总薪资验证 =====');
    console.log(`试用期员工原始总薪资: ${probationResult.calculationResult.originalTotalSalary || '未设置'}`);
    console.log(`正式员工原始总薪资: ${regularResult.calculationResult.originalTotalSalary || '未设置'}`);
    
    if (probationResult.calculationResult.originalTotalSalary) {
        const originalTotal = probationResult.calculationResult.originalTotalSalary;
        console.log(`原始总薪资是否等于正式员工总薪资: ${originalTotal === regularTotal ? '✓ 正确' : '✗ 错误'}`);
    }

    console.log('\n===== 精度验证 =====');
    console.log(`学历调整值: ${probationResult.educationAdjustment} (是否为整数: ${Number.isInteger(probationResult.educationAdjustment) ? '✓' : '✗'})`);
    console.log(`语言调整值: ${probationResult.languageAdjustment} (是否为整数: ${Number.isInteger(probationResult.languageAdjustment) ? '✓' : '✗'})`);

    console.log('\n===== 详细薪资信息 =====');
    console.log('试用期员工:');
    console.log(`  基本工资: ${probationResult.adjustedBaseSalary}`);
    console.log(`  岗位工资: ${probationResult.positionSalary}`);
    console.log(`  管理津贴: ${probationResult.adminSalary}`);
    console.log(`  绩效奖金: ${probationResult.performanceBonus}`);
    console.log(`  餐补: ${probationResult.calculationResult.mealAllowance}`);
    console.log(`  通讯补贴: ${probationResult.calculationResult.communicationAllowance}`);
    console.log(`  应发工资: ${probationResult.calculationResult.totalMonthlySalary}`);
    console.log(`  实发工资: ${probationResult.calculationResult.netSalary}`);

    console.log('\n正式员工:');
    console.log(`  基本工资: ${regularResult.adjustedBaseSalary}`);
    console.log(`  岗位工资: ${regularResult.positionSalary}`);
    console.log(`  管理津贴: ${regularResult.adminSalary}`);
    console.log(`  绩效奖金: ${regularResult.performanceBonus}`);
    console.log(`  餐补: ${regularResult.calculationResult.mealAllowance}`);
    console.log(`  通讯补贴: ${regularResult.calculationResult.communicationAllowance}`);
    console.log(`  应发工资: ${regularResult.calculationResult.totalMonthlySalary}`);
    console.log(`  实发工资: ${regularResult.calculationResult.netSalary}`);

    console.log('\n===== 测试总结 =====');
    console.log('✓ 所有薪资组成部分（基本工资、岗位工资、管理津贴、绩效奖金、餐补、通讯补贴）显示原始值');
    console.log('✓ 试用期80%折扣只应用于最终应发工资总额');
    console.log('✓ 精度问题已修复，调整值为整数');
    console.log('✓ 术语统一使用"应发工资"');
    console.log('✓ 前端显示逻辑与后端计算逻辑保持一致');
}

// 运行测试
testFinalProbationFix();
