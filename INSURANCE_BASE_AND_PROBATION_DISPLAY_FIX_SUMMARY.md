# 社保基数配置和试用期应发工资显示修复总结

## 问题描述

用户反馈了两个关键问题：

1. **试用期应发工资说明缺失**：
   - 在试用期员工的薪资计算结果和薪资详情中，应发工资旁边缺少说明
   - 需要添加"试用期工资为正常工资的80%"的说明

2. **社保基数配置未生效**：
   - 后台管理已设置社保基数类型为使用固定值5000
   - 但薪资计算中仍然使用基本工资作为社保基数
   - 需要修复社保计算逻辑，使其根据配置选择正确的基数

## 修复方案

### 1. 试用期应发工资说明添加 ✅

**SalaryForm.js 修复**：
```javascript
<h4>
    应发工资: {formatCurrency(Number(salaryResult.calculationResult.totalMonthlySalary))}
    {isProbation && (
        <span style={{ 
            fontSize: '12px', 
            color: '#1890ff', 
            marginLeft: '8px',
            fontWeight: 'normal'
        }}>
            (试用期工资为正常工资的80%)
        </span>
    )}
</h4>
```

**SalaryDetail.js 修复**：
```javascript
<div className="salary-result-title">
    应发工资
    {processedEmployee.isProbation && (
        <span style={{ 
            fontSize: '11px', 
            color: '#1890ff', 
            marginLeft: '6px',
            fontWeight: 'normal'
        }}>
            (试用期工资为正常工资的80%)
        </span>
    )}
</div>
```

### 2. 社保基数计算逻辑修复 ✅

**配置读取逻辑**：
```javascript
// 根据配置确定社保基数
let socialInsuranceBase = salary; // 默认使用基本工资

if (CONFIG.INSURANCE_BASE && CONFIG.INSURANCE_BASE.type === 'fixed') {
    socialInsuranceBase = CONFIG.INSURANCE_BASE.fixedAmount || 5000;
    console.log('使用固定社保基数:', socialInsuranceBase);
} else {
    console.log('使用基本工资作为社保基数:', socialInsuranceBase);
}
```

**社保基数应用**：
```javascript
// 计算各项社保基数 - 使用配置的社保基数
const pensionBase = Math.min(Math.max(socialInsuranceBase, pensionMinBase), pensionMaxBase);
const medicalBase = Math.min(Math.max(socialInsuranceBase, medicalMinBase), medicalMaxBase);
const unemploymentBase = Math.min(Math.max(socialInsuranceBase, unemploymentMinBase), unemploymentMaxBase);
```

### 3. 社保缴费基数显示修复 ✅

**SalaryDetail.js 显示逻辑**：
```javascript
<b>社保缴费基数：</b> {resetSalary
    ? formatCurrency(0)
    : (() => {
        // 根据配置确定显示的社保基数
        if (localConfig?.SALARY_CONFIG?.INSURANCE_BASE?.type === 'fixed') {
            return formatCurrency(localConfig.SALARY_CONFIG.INSURANCE_BASE.fixedAmount || 5000);
        } else {
            return formatCurrency(safeNumber(processedEmployee.adjustedBaseSalary));
        }
    })()
}
```

## 修复后的行为

### 试用期应发工资显示
1. **SalaryForm.js**：应发工资旁边显示"(试用期工资为正常工资的80%)"
2. **SalaryDetail.js**：应发工资标题旁边显示"(试用期工资为正常工资的80%)"
3. **颜色和样式**：使用蓝色(#1890ff)，字体较小，不影响主要信息的显示

### 社保基数计算
1. **配置读取**：正确读取 `INSURANCE_BASE.type` 和 `fixedAmount` 配置
2. **基数选择**：
   - 如果 `type === 'fixed'`：使用 `fixedAmount`（5000）作为社保基数
   - 如果 `type === 'baseSalary'` 或未配置：使用基本工资作为社保基数
3. **计算应用**：所有社保项目（养老、医疗、失业、大额医疗补充）都使用统一的基数

### 社保缴费基数显示
1. **固定基数模式**：显示配置的固定金额（5000）
2. **基本工资模式**：显示员工的实际基本工资
3. **动态切换**：根据配置自动选择显示内容

## 测试验证结果

### 社保基数配置验证 ✅
```
社保基数配置:
- 类型: fixed
- 固定金额: 5000
✓ 配置正确：使用固定社保基数 5000
```

### 社保计算验证 ✅
```
固定社保基数验证:
- 试用期员工社保: 515.13
- 正式员工社保: 515.13
- 社保金额是否相等: ✓ 正确

期望的社保计算（基于固定基数5000）:
- 养老保险基数: 5000 金额: 400
- 医疗保险基数: 5000 金额: 100
- 失业保险基数: 5000 金额: 15
- 大额医疗补充: 0.13
- 期望总额: 515.13
- 实际总额: 515.13
- 计算是否正确: ✓ 正确
```

### 试用期显示验证 ✅
```
试用期员工薪资信息:
- 基本工资: 5600 (显示原始值)
- 岗位工资: 2000 (显示原始值)
- 餐补: 400 (显示原始值)
- 通讯补贴: 100 (显示原始值)
- 应发工资: 6480 (显示80%后的值，旁边有说明)
- 原始总薪资: 8100 (试用期员工特有字段)
```

## 文件修改清单

### 后端文件
1. `backend/utils/calculationUtils.js`
   - 修改 `calculateSocialInsurance` 函数
   - 添加社保基数配置读取逻辑
   - 根据配置类型选择使用固定基数或基本工资
   - 增强调试日志，显示实际使用的社保基数

### 前端文件
1. `frontend/src/components/salary/SalaryForm.js`
   - 在应发工资旁边添加试用期说明

2. `frontend/src/components/salary/SalaryDetail.js`
   - 在应发工资标题旁边添加试用期说明
   - 修复社保缴费基数显示逻辑，根据配置显示正确的基数

### 测试文件
1. `test_insurance_base_and_probation_display.js` - 综合测试验证
2. `INSURANCE_BASE_AND_PROBATION_DISPLAY_FIX_SUMMARY.md` - 修复总结

## 验证方法

运行以下命令验证修复效果：
```bash
node test_insurance_base_and_probation_display.js
```

## 总结

✅ **问题完全解决**：
1. **试用期说明**：在薪资计算结果和薪资详情的应发工资旁边添加了清晰的说明
2. **社保基数配置**：修复了社保计算逻辑，正确使用配置的固定基数5000
3. **显示一致性**：社保缴费基数显示与实际计算使用的基数保持一致
4. **配置灵活性**：支持固定基数和基本工资两种模式的动态切换
5. **调试信息**：增强了日志输出，便于问题排查和验证

修复后的系统能够：
- 正确显示试用期员工应发工资的说明信息
- 根据后台配置使用正确的社保基数进行计算
- 在前端正确显示实际使用的社保缴费基数
- 保持试用期和正式员工社保计算的一致性（当使用固定基数时）
