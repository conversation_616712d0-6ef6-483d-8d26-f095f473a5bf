// 测试试用期问题的最终修复
const { calculateMonthlySalary } = require('./backend/utils/SalaryCalculator');

// 模拟前端试用期判断逻辑
function isProbationPeriod(employeeData) {
    if (!employeeData) return false;
    
    // 如果工作类型是试用，直接返回true
    if (employeeData.workType === '试用') return true;
    
    // 如果有isProbation字段且为true，返回true
    if (employeeData.isProbation === true) return true;
    
    // 如果有试用期结束日期，检查当前日期是否在试用期内
    if (employeeData.probationEndDate) {
        const today = new Date();
        const probationEndDate = new Date(employeeData.probationEndDate);
        return today <= probationEndDate;
    }
    
    return false;
}

// 测试函数
function testProbationIssuesFinal() {
    console.log('开始测试试用期问题的最终修复...\n');

    // 测试用例：试用期员工
    const probationEmployee = {
        employeeId: 'TEST001',
        name: '试用期员工',
        department: '工程部',
        subDepartment: '开发组',
        positionType: '技术',
        positionLevel: 'A5',
        education: '本科（普通院校）',
        languageLevel: '熟练',
        administrativeLevel: '无',
        performanceCoefficient: 1.0,
        actualAttendance: 22,
        specialAllowance: { amount: 0 },
        specialDeduction: { amount: 0 },
        // 试用期相关字段
        workType: '试用',
        probationEndDate: '2025-12-31',
        isProbation: true
    };

    // 测试用例：正式员工
    const regularEmployee = {
        ...probationEmployee,
        name: '正式员工',
        employeeId: 'TEST002',
        workType: '全职',
        probationEndDate: '',
        isProbation: false
    };

    console.log('===== 后端计算验证 =====');
    
    console.log('\n试用期员工计算:');
    const probationResult = calculateMonthlySalary(probationEmployee);
    console.log('- 基本工资:', probationResult.adjustedBaseSalary, '(应显示原始值)');
    console.log('- 岗位工资:', probationResult.positionSalary, '(应显示原始值)');
    console.log('- 餐补:', probationResult.calculationResult.mealAllowance, '(应显示原始值)');
    console.log('- 通讯补贴:', probationResult.calculationResult.communicationAllowance, '(应显示原始值)');
    console.log('- 应发工资:', probationResult.calculationResult.totalMonthlySalary, '(应显示80%后的值)');
    console.log('- 原始总薪资:', probationResult.calculationResult.originalTotalSalary, '(试用期员工应有此字段)');
    console.log('- 试用期状态:', probationResult.isProbation);
    console.log('- 试用期系数:', probationResult.probationFactor);
    
    console.log('\n正式员工计算:');
    const regularResult = calculateMonthlySalary(regularEmployee);
    console.log('- 基本工资:', regularResult.adjustedBaseSalary);
    console.log('- 岗位工资:', regularResult.positionSalary);
    console.log('- 餐补:', regularResult.calculationResult.mealAllowance);
    console.log('- 通讯补贴:', regularResult.calculationResult.communicationAllowance);
    console.log('- 应发工资:', regularResult.calculationResult.totalMonthlySalary);
    console.log('- 原始总薪资:', regularResult.calculationResult.originalTotalSalary || '无（正式员工不需要此字段）');
    console.log('- 试用期状态:', regularResult.isProbation);
    console.log('- 试用期系数:', regularResult.probationFactor);

    console.log('\n===== 关键验证点 =====');
    
    // 1. 基本工资应该相同
    const baseSalaryEqual = probationResult.adjustedBaseSalary === regularResult.adjustedBaseSalary;
    console.log('1. 基本工资是否相同:', baseSalaryEqual ? '✓ 正确' : '✗ 错误');
    console.log('   - 试用期员工基本工资:', probationResult.adjustedBaseSalary);
    console.log('   - 正式员工基本工资:', regularResult.adjustedBaseSalary);
    
    // 2. 岗位工资应该相同
    const positionSalaryEqual = probationResult.positionSalary === regularResult.positionSalary;
    console.log('2. 岗位工资是否相同:', positionSalaryEqual ? '✓ 正确' : '✗ 错误');
    console.log('   - 试用期员工岗位工资:', probationResult.positionSalary);
    console.log('   - 正式员工岗位工资:', regularResult.positionSalary);
    
    // 3. 津贴应该相同
    const allowanceEqual = (
        probationResult.calculationResult.mealAllowance === regularResult.calculationResult.mealAllowance &&
        probationResult.calculationResult.communicationAllowance === regularResult.calculationResult.communicationAllowance
    );
    console.log('3. 津贴是否相同:', allowanceEqual ? '✓ 正确' : '✗ 错误');
    console.log('   - 试用期员工餐补:', probationResult.calculationResult.mealAllowance);
    console.log('   - 正式员工餐补:', regularResult.calculationResult.mealAllowance);
    
    // 4. 试用期应发工资应该是正式员工的80%
    const expectedProbationSalary = Math.round(regularResult.calculationResult.totalMonthlySalary * 0.8);
    const probationSalaryCorrect = Math.abs(probationResult.calculationResult.totalMonthlySalary - expectedProbationSalary) < 1;
    console.log('4. 试用期80%计算是否正确:', probationSalaryCorrect ? '✓ 正确' : '✗ 错误');
    console.log('   - 正式员工应发工资:', regularResult.calculationResult.totalMonthlySalary);
    console.log('   - 期望试用期应发工资 (80%):', expectedProbationSalary);
    console.log('   - 实际试用期应发工资:', probationResult.calculationResult.totalMonthlySalary);
    
    // 5. 试用期员工应该有原始总薪资字段
    const hasOriginalTotalSalary = probationResult.calculationResult.originalTotalSalary !== undefined;
    console.log('5. 试用期员工是否有原始总薪资字段:', hasOriginalTotalSalary ? '✓ 正确' : '✗ 错误');
    console.log('   - 试用期员工原始总薪资:', probationResult.calculationResult.originalTotalSalary);

    console.log('\n===== 前端显示验证 =====');
    
    // 模拟前端试用期状态判断
    const frontendProbationStatus = isProbationPeriod(probationEmployee);
    const frontendRegularStatus = isProbationPeriod(regularEmployee);
    
    console.log('前端试用期状态判断:');
    console.log('- 试用期员工:', frontendProbationStatus ? '✓ 正确识别为试用期' : '✗ 错误识别');
    console.log('- 正式员工:', !frontendRegularStatus ? '✓ 正确识别为正式员工' : '✗ 错误识别');
    
    console.log('\n前端应该显示的内容:');
    console.log('SalaryList.js:');
    console.log('- 基本工资列不显示试用期标识');
    console.log('- 工作状态列显示"试用期"标识');
    console.log('- 应发工资显示80%后的值');
    
    console.log('\nSalaryForm.js:');
    console.log('- 基本工资、岗位工资、津贴等显示原始值');
    console.log('- 应发工资显示80%后的值，旁边显示"(试用期工资为正常工资的80%)"');
    console.log('- 保存后再打开仍然显示试用期说明');
    
    console.log('\nSalaryDetail.js:');
    console.log('- 基本工资、岗位工资、津贴等显示原始值');
    console.log('- 应发工资显示80%后的值，旁边显示"(试用期工资为正常工资的80%)"');
    console.log('- 社保缴费基数显示配置的固定值5000');
    console.log('- 试用期说明区域显示相关说明');

    console.log('\n===== 修复总结 =====');
    
    const allCorrect = baseSalaryEqual && positionSalaryEqual && allowanceEqual && probationSalaryCorrect && hasOriginalTotalSalary;
    
    if (allCorrect) {
        console.log('✅ 所有试用期问题已修复！');
        console.log('\n修复内容:');
        console.log('1. ✓ 移除了SalaryList.js基本工资列的试用期标识');
        console.log('2. ✓ 确保基本工资、岗位工资、津贴等显示原始值');
        console.log('3. ✓ 试用期80%只应用于最终应发工资');
        console.log('4. ✓ 修复了前端状态同步问题');
        console.log('5. ✓ 修复了SalaryDetail中的显示问题');
        console.log('6. ✓ 修复了社保缴费基数显示');
    } else {
        console.log('❌ 仍有问题需要解决！');
        console.log('\n问题检查:');
        console.log('- 基本工资相同:', baseSalaryEqual ? '✓' : '✗');
        console.log('- 岗位工资相同:', positionSalaryEqual ? '✓' : '✗');
        console.log('- 津贴相同:', allowanceEqual ? '✓' : '✗');
        console.log('- 试用期80%计算:', probationSalaryCorrect ? '✓' : '✗');
        console.log('- 原始总薪资字段:', hasOriginalTotalSalary ? '✓' : '✗');
    }
    
    console.log('\n下一步操作:');
    console.log('1. 如果数据库中有错误的基本工资数据，运行修复脚本');
    console.log('2. 重新加载前端页面验证显示效果');
    console.log('3. 测试保存后再打开的功能');
    console.log('4. 验证试用期说明的显示');
}

// 运行测试
testProbationIssuesFinal();
