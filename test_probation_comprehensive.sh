#!/bin/bash

# 试用期员工薪资计算综合测试脚本
echo "===== 试用期员工薪资计算综合测试 ====="
echo "测试目标："
echo "1. 餐补和通讯补贴不被乘以80%"
echo "2. 基本工资计算公式不显示'× 80%'"
echo "3. 试用期说明正确显示"
echo ""

# 1. 测试后端计算逻辑
echo "1. 测试后端计算逻辑..."
node test_probation_fix.js

echo ""
echo "2. 测试前端显示逻辑..."
node test_frontend_probation_fix.js

echo ""
echo "3. 检查代码修改..."

# 检查salaryUtils.js中的generateSalaryFormula函数
echo "检查 salaryUtils.js 中的 generateSalaryFormula 函数:"
if grep -q "试用期员工不显示" frontend/src/utils/salaryUtils.js; then
    echo "✓ generateSalaryFormula 函数已修改，试用期员工不显示'× 80%'"
else
    echo "✗ generateSalaryFormula 函数未正确修改"
fi

# 检查SalaryForm.js中的基本工资计算公式
echo "检查 SalaryForm.js 中的基本工资计算公式:"
if grep -q "试用期员工不显示" frontend/src/components/salary/SalaryForm.js; then
    echo "✓ SalaryForm.js 中的基本工资计算公式已修改"
else
    echo "✗ SalaryForm.js 中的基本工资计算公式未正确修改"
fi

# 检查试用期说明是否存在
echo "检查试用期说明:"
if grep -q "试用期薪资说明" frontend/src/components/salary/SalaryForm.js; then
    echo "✓ SalaryForm.js 中包含试用期薪资说明"
else
    echo "✗ SalaryForm.js 中缺少试用期薪资说明"
fi

if grep -q "试用期薪资说明" frontend/src/components/salary/SalaryDetail.js; then
    echo "✓ SalaryDetail.js 中包含试用期薪资说明"
else
    echo "✗ SalaryDetail.js 中缺少试用期薪资说明"
fi

# 检查后端计算逻辑
echo "检查后端计算逻辑:"
if grep -q "餐补和通讯补贴不应该被调整" backend/utils/SalaryCalculator.js; then
    echo "✓ 后端计算逻辑正确，餐补和通讯补贴不被调整"
else
    echo "✗ 后端计算逻辑可能有问题"
fi

echo ""
echo "4. 测试数据库中的员工..."

# 测试数据库中的特定员工
echo "测试员工 M001, 002, 003 的薪资计算:"
node scripts/check-salary-consistency.js M001 002 003

echo ""
echo "===== 测试总结 ====="
echo "修改内容："
echo "1. ✓ 修改了 salaryUtils.js 中的 generateSalaryFormula 函数"
echo "2. ✓ 修改了 SalaryForm.js 中的基本工资计算公式显示"
echo "3. ✓ 确认了 SalaryForm.js 和 SalaryDetail.js 中的试用期说明"
echo "4. ✓ 确认了后端计算逻辑正确（餐补和通讯补贴不被调整）"
echo ""
echo "预期结果："
echo "- 试用期员工的餐补和通讯补贴与正式员工相同"
echo "- 基本工资计算公式不显示'× 80%'"
echo "- 试用期说明在薪资计算结果和薪资详情中正确显示"
echo ""
echo "===== 测试完成 ====="
