#!/bin/bash

# MCHRMS 项目完整备份脚本
# 使用方式: ./backup.sh [选项]
# 选项:
#   --minimal  创建精简备份（排除 node_modules, .git 等）
#   默认创建完整备份（包含所有文件）

# 获取当前日期和时间，格式为 YYYY-MM-DD_HH-MM-SS
CURRENT_DATETIME=$(date +"%Y-%m-%d_%H-%M-%S")

# 项目版本号
VERSION="1.2.2"

# 检查备份类型
BACKUP_TYPE="complete"
if [ "$1" = "--minimal" ]; then
    BACKUP_TYPE="minimal"
fi

# 备份文件名
if [ "$BACKUP_TYPE" = "minimal" ]; then
    BACKUP_FILENAME="mchrms-minimal-backup-${CURRENT_DATETIME}"
else
    BACKUP_FILENAME="mchrms-complete-backup-${CURRENT_DATETIME}"
fi

# 备份目录 - 修改为用户根目录下的 backups
BACKUP_DIR="$HOME/backups"

# 确保备份目录存在
mkdir -p "$BACKUP_DIR"

echo "🚀 开始备份 MCHRMS 项目..."
echo "📦 项目版本: $VERSION"
echo "🔧 备份类型: $BACKUP_TYPE"
echo "📁 备份文件名: $BACKUP_FILENAME.tar.gz"
echo "📂 备份目录: $BACKUP_DIR"
echo "⏰ 备份时间: $CURRENT_DATETIME"

# 获取项目大小
echo "📊 正在计算项目大小..."
PROJECT_SIZE=$(du -sh . 2>/dev/null | cut -f1)
echo "📏 项目总大小: $PROJECT_SIZE"

if [ "$BACKUP_TYPE" = "minimal" ]; then
    echo "🔧 创建精简备份（排除 node_modules, .git, build 等）..."

    # 创建精简备份，排除大文件和不必要的目录
    tar --exclude='node_modules' \
        --exclude='build' \
        --exclude='dist' \
        --exclude='.git' \
        --exclude='*.log' \
        --exclude='data/mongodb.log*' \
        --exclude="$BACKUP_DIR" \
        -czf "$BACKUP_DIR/$BACKUP_FILENAME.tar.gz" \
        .
else
    echo "📦 创建完整备份（包含所有文件）..."
    echo "⚠️  注意：完整备份包含 node_modules，文件较大，请耐心等待..."

    # 创建完整备份，只排除当前备份文件本身
    tar --exclude="$BACKUP_DIR/$BACKUP_FILENAME.tar.gz" \
        -czf "$BACKUP_DIR/$BACKUP_FILENAME.tar.gz" \
        .
fi

# 检查备份是否成功
if [ -f "$BACKUP_DIR/$BACKUP_FILENAME.tar.gz" ]; then
    BACKUP_SIZE=$(du -h "$BACKUP_DIR/$BACKUP_FILENAME.tar.gz" | cut -f1)
    echo ""
    echo "✅ 备份成功完成！"
    echo "📁 备份文件: $BACKUP_DIR/$BACKUP_FILENAME.tar.gz"
    echo "📏 备份大小: $BACKUP_SIZE"
    echo "⏰ 完成时间: $(date +"%Y-%m-%d %H:%M:%S")"

    # 创建备份信息文件
    INFO_FILE="$BACKUP_DIR/$BACKUP_FILENAME.info"
    cat > "$INFO_FILE" << EOF
# MCHRMS 项目备份信息

## 备份详情
- **备份文件**: $BACKUP_FILENAME.tar.gz
- **备份时间**: $CURRENT_DATETIME
- **项目版本**: $VERSION
- **备份类型**: $BACKUP_TYPE
- **原始大小**: $PROJECT_SIZE
- **备份大小**: $BACKUP_SIZE

## 备份内容
EOF

    if [ "$BACKUP_TYPE" = "minimal" ]; then
        cat >> "$INFO_FILE" << EOF
精简备份，排除以下内容：
- node_modules/ (依赖包)
- build/, dist/ (构建输出)
- .git/ (版本控制)
- *.log (日志文件)

## 恢复说明
1. 解压备份文件
2. 安装依赖：npm install
3. 安装后端依赖：cd backend && npm install
4. 安装前端依赖：cd ../frontend && npm install
5. 启动项目：cd .. && node start.js
EOF
    else
        cat >> "$INFO_FILE" << EOF
完整备份，包含所有文件：
- ✅ node_modules/ (所有依赖包)
- ✅ .git/ (版本控制信息)
- ✅ build/, dist/ (构建输出)
- ✅ *.log (日志文件)
- ✅ 所有项目文件

## 恢复说明
1. 解压备份文件即可使用
2. 直接启动：node start.js
EOF
    fi

    echo "📄 备份信息文件: $INFO_FILE"

else
    echo ""
    echo "❌ 备份失败！"
    echo "请检查磁盘空间和权限设置"
    exit 1
fi

echo ""
echo "📂 备份目录中的所有备份文件:"
ls -lht "$BACKUP_DIR" | grep "mchrms-" | head -10

echo ""
echo "🎉 备份操作完成！"
echo "💡 提示："
echo "   - 完整备份: ./backup.sh"
echo "   - 精简备份: ./backup.sh --minimal"
echo "   - 备份位置: $BACKUP_DIR"

exit 0
