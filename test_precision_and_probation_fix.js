// 测试精度问题和试用期80%计算修复
const { calculateMonthlySalary } = require('./backend/utils/SalaryCalculator');

// 测试函数
function testPrecisionAndProbationFix() {
    console.log('开始测试精度问题和试用期80%计算修复...\n');

    // 测试用例1：试用期员工 - 精通语言
    const probationEmployee = {
        employeeId: 'TEST001',
        name: '试用期员工',
        department: '工程部',
        subDepartment: '开发组',
        positionType: '技术',
        positionLevel: 'A5',
        education: '本科（普通院校）',
        languageLevel: '精通', // 系数1.3，应该产生1050的调整值
        administrativeLevel: '无',
        performanceCoefficient: 1.0,
        actualAttendance: 22,
        specialAllowance: { amount: 0 },
        specialDeduction: { amount: 0 },
        // 试用期相关字段
        workType: '试用',
        probationEndDate: '2025-12-31',
        isProbation: true
    };

    // 测试用例2：正式员工 - 相同条件
    const regularEmployee = {
        ...probationEmployee,
        name: '正式员工',
        workType: '全职',
        probationEndDate: '',
        isProbation: false
    };

    console.log('测试员工信息:');
    console.log('- 学历:', probationEmployee.education);
    console.log('- 语言水平:', probationEmployee.languageLevel, '(系数应为1.3)');
    console.log('- 预期语言调整值: 3500 × (1.3 - 1) = 1050');

    // 计算薪资
    console.log('\n计算试用期员工薪资...');
    const probationResult = calculateMonthlySalary(probationEmployee);

    console.log('\n计算正式员工薪资...');
    const regularResult = calculateMonthlySalary(regularEmployee);

    console.log('\n===== 精度问题验证 =====');
    
    // 验证语言调整值精度
    console.log('试用期员工语言调整值:', probationResult.languageAdjustment);
    console.log('正式员工语言调整值:', regularResult.languageAdjustment);
    console.log('语言调整值是否为整数:', Number.isInteger(probationResult.languageAdjustment) ? '✓ 正确' : '✗ 错误');
    console.log('语言调整值是否等于1050:', probationResult.languageAdjustment === 1050 ? '✓ 正确' : '✗ 错误');

    // 验证学历调整值精度
    console.log('试用期员工学历调整值:', probationResult.educationAdjustment);
    console.log('正式员工学历调整值:', regularResult.educationAdjustment);
    console.log('学历调整值是否为整数:', Number.isInteger(probationResult.educationAdjustment) ? '✓ 正确' : '✗ 错误');

    console.log('\n===== 试用期80%计算验证 =====');
    
    // 验证餐补和通讯补贴
    console.log('试用期员工餐补:', probationResult.calculationResult.mealAllowance);
    console.log('正式员工餐补:', regularResult.calculationResult.mealAllowance);
    console.log('餐补是否相等:', probationResult.calculationResult.mealAllowance === regularResult.calculationResult.mealAllowance ? '✓ 正确' : '✗ 错误');

    console.log('试用期员工通讯补贴:', probationResult.calculationResult.communicationAllowance);
    console.log('正式员工通讯补贴:', regularResult.calculationResult.communicationAllowance);
    console.log('通讯补贴是否相等:', probationResult.calculationResult.communicationAllowance === regularResult.calculationResult.communicationAllowance ? '✓ 正确' : '✗ 错误');

    // 验证80%计算逻辑
    console.log('\n===== 80%计算逻辑验证 =====');
    
    // 计算正式员工的主要薪资部分（不包括餐补和通讯补贴）
    const regularMainSalary = regularResult.originalBaseSalary + 
                             regularResult.originalPositionSalary + 
                             regularResult.originalAdminSalary + 
                             regularResult.originalPerformanceBonus;
    
    const expectedProbationMainSalary = Math.round(regularMainSalary * 0.8);
    
    // 计算试用期员工的主要薪资部分
    const probationMainSalary = probationResult.adjustedBaseSalary + 
                                probationResult.positionSalary + 
                                probationResult.adminSalary + 
                                probationResult.performanceBonus;
    
    console.log('正式员工主要薪资部分:', regularMainSalary);
    console.log('期望的试用期主要薪资 (80%):', expectedProbationMainSalary);
    console.log('实际的试用期主要薪资:', probationMainSalary);
    console.log('主要薪资80%计算是否正确:', Math.abs(probationMainSalary - expectedProbationMainSalary) < 1 ? '✓ 正确' : '✗ 错误');

    // 验证总薪资
    const regularTotalSalary = regularResult.calculationResult.totalMonthlySalary;
    const probationTotalSalary = probationResult.calculationResult.totalMonthlySalary;
    const expectedProbationTotal = expectedProbationMainSalary + 400 + 100; // 主要薪资80% + 餐补 + 通讯补贴
    
    console.log('\n正式员工总薪资:', regularTotalSalary);
    console.log('试用期员工总薪资:', probationTotalSalary);
    console.log('期望的试用期总薪资:', expectedProbationTotal);
    console.log('总薪资计算是否正确:', Math.abs(probationTotalSalary - expectedProbationTotal) < 1 ? '✓ 正确' : '✗ 错误');

    console.log('\n===== 详细薪资对比 =====');
    console.log('正式员工:');
    console.log('  基本工资:', regularResult.adjustedBaseSalary);
    console.log('  岗位工资:', regularResult.positionSalary);
    console.log('  管理津贴:', regularResult.adminSalary);
    console.log('  绩效奖金:', regularResult.performanceBonus);
    console.log('  餐补:', regularResult.calculationResult.mealAllowance);
    console.log('  通讯补贴:', regularResult.calculationResult.communicationAllowance);
    console.log('  总薪资:', regularResult.calculationResult.totalMonthlySalary);

    console.log('\n试用期员工:');
    console.log('  基本工资:', probationResult.adjustedBaseSalary);
    console.log('  岗位工资:', probationResult.positionSalary);
    console.log('  管理津贴:', probationResult.adminSalary);
    console.log('  绩效奖金:', probationResult.performanceBonus);
    console.log('  餐补:', probationResult.calculationResult.mealAllowance);
    console.log('  通讯补贴:', probationResult.calculationResult.communicationAllowance);
    console.log('  总薪资:', probationResult.calculationResult.totalMonthlySalary);

    console.log('\n===== 测试完成 =====');
    console.log('修复总结:');
    console.log('1. 语言调整值精度问题已修复，结果为整数');
    console.log('2. 学历调整值精度问题已修复，结果为整数');
    console.log('3. 试用期80%计算逻辑已修复，基于应发税前工资计算');
    console.log('4. 餐补和通讯补贴不受试用期影响');
}

// 运行测试
testPrecisionAndProbationFix();
